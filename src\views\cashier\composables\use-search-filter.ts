import { ref, computed } from "vue";
import type { Room, AreaConfig } from "../types/room";
import { AREA_CONFIG, AREA_DISPLAY_CONFIG } from "../constants/room-config";

export function useSearchFilter() {
  // 搜索关键词
  const searchKeyword = ref("");
  
  // 当前选中的区域
  const currentArea = ref("all");

  // 区域配置
  const areas = AREA_CONFIG;

  // 计算属性 - 过滤后的区域配置（用于显示）
  const filteredAreas = computed(() => {
    const areaConfigs = Object.values(AREA_DISPLAY_CONFIG);
    
    return areaConfigs
      .map(config => ({
        ...config,
        rooms: [] // 这里会在使用时填充房间数据
      }))
      .filter(
        area => currentArea.value === "all" || area.key === currentArea.value
      );
  });

  // 处理搜索
  const handleSearch = () => {
    // 搜索逻辑已在计算属性中处理
  };

  // 切换区域
  const switchArea = (area: string) => {
    currentArea.value = area;
  };

  // 按状态筛选
  const filterByStatus = (status: string) => {
    // TODO: 实现状态筛选逻辑
    console.log(`筛选${status}状态的房间`);
  };

  return {
    // 数据
    searchKeyword,
    currentArea,
    areas,
    
    // 计算属性
    filteredAreas,
    
    // 方法
    handleSearch,
    switchArea,
    filterByStatus
  };
}

<template>
  <div class="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-2xl">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-slate-200">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
            <font-awesome-icon icon="user-plus" class="text-white" />
          </div>
          <div>
            <h2 class="text-xl font-bold text-slate-800">新增顾客</h2>
            <p class="text-sm text-slate-500">添加新的顾客信息</p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="w-8 h-8 flex items-center justify-center text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
        >
          <font-awesome-icon icon="times" />
        </button>
      </div>

      <!-- 表单内容 -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- 基本信息 -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              顾客姓名 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="请输入顾客姓名"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              联系电话 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.phone"
              type="tel"
              required
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="请输入联系电话"
            />
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              电子邮箱
            </label>
            <input
              v-model="form.email"
              type="email"
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="请输入电子邮箱"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">
              性别
            </label>
            <select
              v-model="form.gender"
              class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="">请选择性别</option>
              <option value="male">男</option>
              <option value="female">女</option>
              <option value="other">其他</option>
            </select>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-slate-700 mb-2">
            生日
          </label>
          <input
            v-model="form.birthday"
            type="date"
            class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        <!-- 备注 -->
        <div>
          <label class="block text-sm font-medium text-slate-700 mb-2">
            备注信息
          </label>
          <textarea
            v-model="form.notes"
            rows="3"
            class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            placeholder="请输入备注信息（可选）"
          ></textarea>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-slate-200">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="!isFormValid"
            class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            确认添加
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { CustomerForm } from '../../types/customer';

defineOptions({
  name: 'CustomerForm'
});

// Emits
const emit = defineEmits<{
  close: [];
  submit: [data: CustomerForm];
}>();

// 表单数据
const form = ref<CustomerForm>({
  name: '',
  phone: '',
  email: '',
  gender: undefined,
  birthday: '',
  notes: ''
});

// 表单验证
const isFormValid = computed(() => {
  return form.value.name.trim() && form.value.phone.trim();
});

// 提交表单
const handleSubmit = () => {
  if (isFormValid.value) {
    emit('submit', form.value);
  }
};
</script>

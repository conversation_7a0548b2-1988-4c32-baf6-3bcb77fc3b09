import { ref } from "vue";

export function useDialogState() {
  // 弹窗状态管理
  const checkinDialogVisible = ref(false);
  const checkoutDialogVisible = ref(false);
  const memberDialogVisible = ref(false);
  const bookingDialogVisible = ref(false);
  const transferDialogVisible = ref(false);

  // 当前操作的房间
  const currentOperationRoom = ref(null);

  // 打开开房弹窗
  const openCheckinDialog = (room = null) => {
    currentOperationRoom.value = room;
    checkinDialogVisible.value = true;
  };

  // 关闭开房弹窗
  const closeCheckinDialog = () => {
    checkinDialogVisible.value = false;
    currentOperationRoom.value = null;
  };

  // 打开结账弹窗
  const openCheckoutDialog = (room = null) => {
    currentOperationRoom.value = room;
    checkoutDialogVisible.value = true;
  };

  // 关闭结账弹窗
  const closeCheckoutDialog = () => {
    checkoutDialogVisible.value = false;
    currentOperationRoom.value = null;
  };

  // 打开会员弹窗
  const openMemberDialog = () => {
    memberDialogVisible.value = true;
  };

  // 关闭会员弹窗
  const closeMemberDialog = () => {
    memberDialogVisible.value = false;
  };

  // 打开预订弹窗
  const openBookingDialog = (room = null) => {
    currentOperationRoom.value = room;
    bookingDialogVisible.value = true;
  };

  // 关闭预订弹窗
  const closeBookingDialog = () => {
    bookingDialogVisible.value = false;
    currentOperationRoom.value = null;
  };

  // 打开换房弹窗
  const openTransferDialog = (room = null) => {
    currentOperationRoom.value = room;
    transferDialogVisible.value = true;
  };

  // 关闭换房弹窗
  const closeTransferDialog = () => {
    transferDialogVisible.value = false;
    currentOperationRoom.value = null;
  };

  // 关闭所有弹窗
  const closeAllDialogs = () => {
    checkinDialogVisible.value = false;
    checkoutDialogVisible.value = false;
    memberDialogVisible.value = false;
    bookingDialogVisible.value = false;
    transferDialogVisible.value = false;
    currentOperationRoom.value = null;
  };

  return {
    // 弹窗状态
    checkinDialogVisible,
    checkoutDialogVisible,
    memberDialogVisible,
    bookingDialogVisible,
    transferDialogVisible,
    currentOperationRoom,

    // 弹窗操作方法
    openCheckinDialog,
    closeCheckinDialog,
    openCheckoutDialog,
    closeCheckoutDialog,
    openMemberDialog,
    closeMemberDialog,
    openBookingDialog,
    closeBookingDialog,
    openTransferDialog,
    closeTransferDialog,
    closeAllDialogs
  };
}

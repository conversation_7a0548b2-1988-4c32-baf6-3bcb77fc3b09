<template>
  <el-dialog
    v-model="dialogVisible"
    title="开房信息"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="room-opening-form">
      <!-- 房间基本信息 -->
      <div class="room-info bg-blue-50 p-4 rounded-lg mb-6">
        <div class="flex justify-between items-center">
          <h3 class="text-xl font-bold">
            房间: {{ roomInfo?.id }} ({{ roomInfo?.type }})
          </h3>
          <el-tag size="large" type="success">可用</el-tag>
        </div>
        <div class="text-gray-600 mt-2">
          {{ currentTime }}
        </div>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
        class="room-form"
      >
        <!-- 客户信息 -->
        <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
          <h4 class="text-lg font-medium mb-4 flex items-center">
            <el-icon class="mr-2"><UserFilled /></el-icon>客户信息
          </h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="客户类型" prop="customerType">
              <el-radio-group v-model="form.customerType">
                <el-radio label="散客">散客</el-radio>
                <el-radio label="会员">会员</el-radio>
                <el-radio label="团体">团体</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="会员号" v-if="form.customerType === '会员'">
              <el-input 
                v-model="form.membershipId" 
                placeholder="请输入会员号"
                clearable>
                <template #append>
                  <el-button :icon="Search" @click="searchMember">查询</el-button>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="客户姓名" prop="customerName">
              <el-input v-model="form.customerName" placeholder="请输入客户姓名" />
            </el-form-item>
            
            <el-form-item label="联系电话">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </div>
        </div>

        <!-- 时段选择 -->
        <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
          <h4 class="text-lg font-medium mb-4 flex items-center">
            <el-icon class="mr-2"><Timer /></el-icon>时段选择
          </h4>
          
          <el-form-item label="选择时段" prop="timeSlot">
            <el-select 
              v-model="form.timeSlot" 
              placeholder="请选择时段"
              style="width: 100%"
            >
              <el-option 
                v-for="slot in timeSlots" 
                :key="slot.value" 
                :label="slot.label" 
                :value="slot.value" 
              />
            </el-select>
          </el-form-item>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <el-form-item label="消费类型" prop="consumptionType">
              <el-radio-group v-model="form.consumptionType">
                <el-radio label="自助餐">自助餐</el-radio>
                <el-radio label="原费+自助">原费+自助</el-radio>
                <el-radio label="原费">原费</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="大人数量">
              <el-input-number 
                v-model="form.adultCount" 
                :min="0" 
                :max="20"
                controls-position="right"
              />
            </el-form-item>
            
            <el-form-item label="小孩数量">
              <el-input-number 
                v-model="form.childCount" 
                :min="0" 
                :max="20"
                controls-position="right"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 附加选项 -->
        <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
          <h4 class="text-lg font-medium mb-4 flex items-center">
            <el-icon class="mr-2"><Setting /></el-icon>附加选项
          </h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="折扣">
              <el-input-number 
                v-model="form.discount" 
                :min="0" 
                :max="100"
                :step="5"
                controls-position="right"
              >
                <template #suffix>%</template>
              </el-input-number>
            </el-form-item>
            
            <el-form-item label="固定折扣">
              <el-input-number 
                v-model="form.fixedDiscount" 
                :min="0" 
                controls-position="right"
              >
                <template #prefix>¥</template>
              </el-input-number>
            </el-form-item>
          </div>
          
          <div class="mt-4">
            <el-checkbox v-model="form.autoDiscount">自动折扣</el-checkbox>
            <el-checkbox v-model="form.freeRoom" class="ml-4">免房费</el-checkbox>
            <el-checkbox v-model="form.useAllInclusive" class="ml-4">使用全单折扣/使用费率金额</el-checkbox>
          </div>
          
          <el-form-item label="备注" class="mt-4">
            <el-input 
              v-model="form.notes" 
              type="textarea" 
              :rows="2"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确认开房
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { UserFilled, Timer, Setting, Search } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import type { Room } from '../../types/room';
import { useRoomOperations } from '../../composables/use-room-operations';

// 房间操作
const roomOperations = useRoomOperations();

// Props
interface Props {
  visible: boolean;
  roomInfo: Room | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [roomId: string, formData: any];
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 对话框可见状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 当前时间
const currentTime = computed(() => {
  const now = new Date();
  return now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
});

// 时段选项
const timeSlots = [
  { label: '11:50 - 14:50 (午餐)', value: '11:50-14:50' },
  { label: '13:30 - 16:30 (下午)', value: '13:30-16:30' },
  { label: '15:00 - 18:00 (晚餐)', value: '15:00-18:00' },
  { label: '18:30 - 21:30 (晚餐)', value: '18:30-21:30' },
  { label: '全天', value: 'all-day' }
];

// 表单数据
const form = reactive({
  customerType: '散客',
  membershipId: '',
  customerName: '',
  phone: '',
  timeSlot: '11:50-14:50',
  consumptionType: '自助餐',
  adultCount: 0,
  childCount: 0,
  discount: 100,
  fixedDiscount: 0,
  autoDiscount: false,
  freeRoom: false,
  useAllInclusive: false,
  notes: ''
});

// 表单验证规则
const rules = reactive<FormRules>({
  customerType: [
    { required: true, message: '请选择客户类型', trigger: 'change' }
  ],
  timeSlot: [
    { required: true, message: '请选择时段', trigger: 'change' }
  ],
  consumptionType: [
    { required: true, message: '请选择消费类型', trigger: 'change' }
  ]
});

// 加载状态
const loading = ref(false);

// 查询会员
const searchMember = () => {
  if (!form.membershipId) {
    ElMessage.warning('请输入会员号');
    return;
  }
  
  // 模拟查询会员信息
  loading.value = true;
  setTimeout(() => {
    form.customerName = '张三';
    form.phone = '13800138000';
    loading.value = false;
    ElMessage.success('会员信息加载成功');
  }, 800);
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 确认开房
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      
      // 模拟API调用
      setTimeout(() => {
        if (props.roomInfo) {
          // 调用房间操作的开房方法
          roomOperations.openRoom(props.roomInfo.id, form.customerName || form.customerType);
          
          // 发送确认事件
          emit('confirm', props.roomInfo.id, form);
          
          // 关闭对话框
          dialogVisible.value = false;
          ElMessage.success(`已成功开通房间 ${props.roomInfo.id}`);
        }
        loading.value = false;
      }, 1000);
    } else {
      ElMessage.error('请完成必填项');
    }
  });
};

// 初始化
onMounted(() => {
  // 可以在这里添加初始化逻辑
});
</script>

<style scoped>
.room-opening-form {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 10px;
}

.room-opening-form::-webkit-scrollbar {
  width: 6px;
}

.room-opening-form::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}

.room-opening-form::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}
</style>

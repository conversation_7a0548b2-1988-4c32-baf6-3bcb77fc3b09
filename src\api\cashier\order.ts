import { cashierHttp } from "./http";
import type { CashierApiResponse, CashierPaginatedResponse } from "./types";
import type { Order, OrderStatus, OrderForm } from "@/views/cashier/types/product";

/**
 * 收银系统订单API
 * 处理所有与订单相关的API请求
 */

// 订单查询参数
export interface OrderQueryParams {
  roomId?: string;
  status?: OrderStatus;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
  keyword?: string;
}

// 订单创建参数
export interface CreateOrderParams extends OrderForm {
  // 可以扩展额外的创建参数
}

// 订单更新参数
export interface UpdateOrderParams {
  status?: OrderStatus;
  notes?: string;
  operator?: string;
}

// 结账参数
export interface CheckoutParams {
  totalAmount: number;
  paymentMethod: string;
  notes?: string;
  discountAmount?: number;
  discountReason?: string;
}

/**
 * 创建订单
 */
export const createOrder = (params: CreateOrderParams): Promise<CashierApiResponse<Order>> => {
  return cashierHttp.post<CashierApiResponse<Order>>("/orders", { data: params });
};

/**
 * 获取订单详情
 */
export const getOrderById = (orderId: string): Promise<CashierApiResponse<Order>> => {
  return cashierHttp.get<CashierApiResponse<Order>>(`/orders/${orderId}`);
};

/**
 * 获取房间的所有订单
 */
export const getOrdersByRoom = (roomId: string): Promise<CashierApiResponse<Order[]>> => {
  return cashierHttp.get<CashierApiResponse<Order[]>>(`/orders/room/${roomId}`);
};

/**
 * 分页获取订单列表
 */
export const getOrdersPaginated = (params: OrderQueryParams): Promise<CashierPaginatedResponse<Order>> => {
  return cashierHttp.get<CashierPaginatedResponse<Order>>("/orders/paginated", { params });
};

/**
 * 获取待处理订单
 */
export const getPendingOrders = (): Promise<CashierApiResponse<Order[]>> => {
  return cashierHttp.get<CashierApiResponse<Order[]>>("/orders/pending");
};

/**
 * 按状态获取订单
 */
export const getOrdersByStatus = (status: OrderStatus): Promise<CashierApiResponse<Order[]>> => {
  return cashierHttp.get<CashierApiResponse<Order[]>>(`/orders/status/${status}`);
};

/**
 * 更新订单状态
 */
export const updateOrderStatus = (
  orderId: string, 
  params: UpdateOrderParams
): Promise<CashierApiResponse<Order>> => {
  return cashierHttp.patch<CashierApiResponse<Order>>(`/orders/${orderId}/status`, { data: params });
};

/**
 * 取消订单
 */
export const cancelOrder = (
  orderId: string, 
  reason?: string
): Promise<CashierApiResponse<Order>> => {
  return cashierHttp.post<CashierApiResponse<Order>>(`/orders/${orderId}/cancel`, { 
    data: { reason } 
  });
};

/**
 * 完成订单
 */
export const completeOrder = (
  orderId: string,
  params?: { notes?: string; operator?: string }
): Promise<CashierApiResponse<Order>> => {
  return cashierHttp.post<CashierApiResponse<Order>>(`/orders/${orderId}/complete`, { data: params });
};

/**
 * 更新订单备注
 */
export const updateOrderNotes = (
  orderId: string, 
  notes: string
): Promise<CashierApiResponse<Order>> => {
  return cashierHttp.patch<CashierApiResponse<Order>>(`/orders/${orderId}/notes`, { 
    data: { notes } 
  });
};

/**
 * 房间订单结账
 */
export const checkoutRoomOrders = (
  roomId: string, 
  params: CheckoutParams
): Promise<CashierApiResponse<{
  success: boolean;
  message: string;
  ordersCount: number;
  totalAmount: number;
  transactionId: string;
}>> => {
  return cashierHttp.post<CashierApiResponse<any>>(`/orders/room/${roomId}/checkout`, { data: params });
};

/**
 * 批量更新订单状态
 */
export const batchUpdateOrderStatus = (
  orderIds: string[],
  status: OrderStatus,
  params?: { notes?: string; operator?: string }
): Promise<CashierApiResponse<{
  successCount: number;
  failedCount: number;
  results: Array<{ orderId: string; success: boolean; message?: string }>;
}>> => {
  return cashierHttp.post<CashierApiResponse<any>>("/orders/batch/status", {
    data: { orderIds, status, ...params }
  });
};

/**
 * 批量取消订单
 */
export const batchCancelOrders = (
  orderIds: string[],
  reason?: string
): Promise<CashierApiResponse<{
  successCount: number;
  failedCount: number;
  results: Array<{ orderId: string; success: boolean; message?: string }>;
}>> => {
  return cashierHttp.post<CashierApiResponse<any>>("/orders/batch/cancel", {
    data: { orderIds, reason }
  });
};

/**
 * 获取订单统计信息
 */
export const getOrderStatistics = (params?: {
  startDate?: string;
  endDate?: string;
  roomId?: string;
}): Promise<CashierApiResponse<{
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
}>> => {
  return cashierHttp.get<CashierApiResponse<any>>("/orders/statistics", { params });
};

/**
 * 搜索订单
 */
export const searchOrders = (params: {
  keyword: string;
  searchType?: "customer" | "roomId" | "orderId" | "all";
  page?: number;
  pageSize?: number;
}): Promise<CashierPaginatedResponse<Order>> => {
  return cashierHttp.get<CashierPaginatedResponse<Order>>("/orders/search", { params });
};

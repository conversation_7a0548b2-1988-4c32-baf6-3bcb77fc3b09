<template>
  <aside class="space-y-6">
    <!-- 状态总览 -->
    <div
      class="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-sm border border-slate-200"
    >
      <h3 class="font-bold mb-4 text-slate-800 flex items-center">
        <font-awesome-icon icon="chart-bar" class="mr-2 text-blue-500" />
        状态总览
      </h3>
      <div class="grid grid-cols-2 gap-3 text-center">
        <div
          v-for="status in roomStatuses"
          :key="status.key"
          :class="[
            'p-3 rounded-lg cursor-pointer transition-all duration-200 hover:scale-105',
            getStatusCardClass(status.color),
            props.currentStatusFilter === status.key
              ? 'ring-2 ring-blue-500 ring-offset-2'
              : ''
          ]"
          @click="filterByStatus(status.key)"
        >
          <div class="text-2xl font-bold">{{ status.count }}</div>
          <div class="text-sm">{{ status.label }}</div>
          <div
            v-if="props.currentStatusFilter === status.key"
            class="text-xs text-blue-600 mt-1"
          >
            ✓ 已筛选
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div
      class="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-sm border border-slate-200"
    >
      <h3 class="font-bold mb-4 text-slate-800 flex items-center">
        <font-awesome-icon icon="bolt" class="mr-2 text-amber-500" />
        快捷操作
      </h3>
      <div class="space-y-2">
        <button
          v-for="action in quickActions"
          :key="action.key"
          class="w-full flex items-center justify-start px-4 py-2 text-slate-700 bg-white hover:bg-slate-100 rounded-lg transition-colors duration-200 border border-slate-200 hover:border-slate-300"
          @click="handleQuickAction(action.key)"
        >
          <font-awesome-icon :icon="action.icon" class="mr-2 text-slate-500" />
          {{ action.label }}
        </button>
      </div>
    </div>

    <!-- 最近开房 -->
    <div
      class="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-sm border border-slate-200"
    >
      <h3 class="font-bold mb-4 text-slate-800 flex items-center">
        <font-awesome-icon icon="clock" class="mr-2 text-emerald-500" />
        最近开房
      </h3>
      <div class="space-y-3">
        <div
          v-for="record in recentOpenings"
          :key="record.roomId"
          class="flex items-center justify-between p-2 hover:bg-slate-50 rounded-lg transition-colors cursor-pointer"
        >
          <div>
            <div class="font-medium text-slate-800">{{ record.roomId }}</div>
            <div class="text-sm text-slate-500">{{ record.roomType }}</div>
          </div>
          <div class="text-sm text-slate-400">{{ record.time }}</div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import type {
  RoomStatusConfig,
  QuickAction,
  RecentOpening
} from "../../types/room";
import { getStatusCardClass } from "../../utils/room-helpers";

defineOptions({
  name: "InfoSidebar"
});

// Props
interface Props {
  roomStatuses: RoomStatusConfig[];
  quickActions: QuickAction[];
  recentOpenings: RecentOpening[];
  currentStatusFilter: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  filterByStatus: [status: string];
  quickAction: [action: string];
}>();

// 按状态筛选
const filterByStatus = (status: string) => {
  emit("filterByStatus", status);
};

// 处理快捷操作
const handleQuickAction = (action: string) => {
  emit("quickAction", action);
};
</script>

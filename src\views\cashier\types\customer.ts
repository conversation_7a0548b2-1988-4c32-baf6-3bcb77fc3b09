// 顾客等级
export type CustomerLevel = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';

// 顾客性别
export type CustomerGender = 'male' | 'female' | 'other';

// 顾客信息
export interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  gender?: CustomerGender;
  birthday?: string;
  level: CustomerLevel;
  points: number;
  totalConsumption: number;
  visitCount: number;
  lastVisitDate?: string;
  membershipDate: string;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 顾客表单数据
export interface CustomerForm {
  name: string;
  phone: string;
  email?: string;
  gender?: CustomerGender;
  birthday?: string;
  notes?: string;
}

// 顾客查询参数
export interface CustomerQuery {
  keyword?: string; // 姓名或手机号
  level?: CustomerLevel;
  gender?: CustomerGender;
  isActive?: boolean;
  registrationDateFrom?: string;
  registrationDateTo?: string;
  page?: number;
  pageSize?: number;
}

// 消费记录
export interface ConsumptionRecord {
  id: string;
  customerId: string;
  customerName: string;
  roomId: string;
  roomType: string;
  startTime: string;
  endTime: string;
  duration: number;
  baseAmount: number;
  discountAmount: number;
  totalAmount: number;
  pointsEarned: number;
  pointsUsed: number;
  paymentMethod: string;
  notes?: string;
  createdAt: string;
  cashierName: string;
}

// 消费记录查询参数
export interface ConsumptionQuery {
  customerId?: string;
  customerName?: string;
  customerPhone?: string;
  roomId?: string;
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
  paymentMethod?: string;
  page?: number;
  pageSize?: number;
}

// 顾客统计数据
export interface CustomerStats {
  totalCustomers: number;
  activeCustomers: number;
  newCustomersThisMonth: number;
  levelDistribution: Record<CustomerLevel, number>;
  averageConsumption: number;
  topCustomers: Customer[];
}

// 积分记录
export interface PointRecord {
  id: string;
  customerId: string;
  type: 'earn' | 'redeem' | 'expire' | 'adjust';
  points: number;
  description: string;
  relatedOrderId?: string;
  createdAt: string;
  expiresAt?: string;
}

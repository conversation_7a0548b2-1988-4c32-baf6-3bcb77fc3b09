<template>
  <div
    class="bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-sm border border-slate-200 sticky top-24"
    style="height: calc(100vh - 7rem)"
  >
    <!-- 默认状态 -->
    <div
      v-if="!selectedRoom"
      class="flex flex-col items-center justify-center h-full text-center"
    >
      <div
        class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-4"
      >
        <font-awesome-icon
          icon="mouse-pointer"
          class="text-3xl text-gray-400"
        />
      </div>
      <h4 class="text-lg font-medium text-gray-600 mb-2">选择房间</h4>
      <p class="text-gray-500 text-sm">请点击左侧房间查看详细信息</p>
    </div>

    <!-- 选中房间详情 -->
    <div v-else class="flex flex-col h-full space-y-4">
      <!-- 房间信息头部 -->
      <div class="flex-shrink-0 border-b border-gray-200 pb-4">
        <div class="flex justify-between items-start mb-3">
          <div>
            <h3 class="text-2xl font-bold text-gray-800 mb-1">
              房间 {{ selectedRoom.id }}
            </h3>
            <p class="text-sm text-gray-600">{{ selectedRoom.type }}</p>
          </div>
          <span
            :class="[
              'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
              getStatusTagClass(selectedRoom.status)
            ]"
          >
            {{ getStatusText(selectedRoom.status) }}
          </span>
        </div>
      </div>

      <!-- 房间详细信息 -->
      <div
        v-if="selectedRoom.status === 'occupied'"
        class="flex-shrink-0 space-y-3"
      >
        <div class="bg-blue-50 rounded-lg p-3">
          <h4 class="font-medium text-blue-800 mb-2">消费信息</h4>
          <div class="space-y-1 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">客户：</span>
              <span class="font-medium">{{ selectedRoom.customer }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">开始时间：</span>
              <span>{{ selectedRoom.startTime }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">已用时长：</span>
              <span>{{ selectedRoom.duration }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">当前消费：</span>
              <span class="font-bold text-orange-600"
                >¥{{ selectedRoom.total }}</span
              >
            </div>
          </div>
        </div>

        <!-- 订单信息 -->
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="flex items-center justify-between mb-2">
            <h4 class="font-medium text-gray-800">订单信息</h4>
            <span class="text-xs text-gray-500">
              {{ orders.length }} 个订单
            </span>
          </div>

          <div v-if="orders.length === 0" class="text-center py-4">
            <font-awesome-icon
              icon="shopping-cart"
              class="text-2xl text-gray-300 mb-2"
            />
            <p class="text-sm text-gray-500">暂无订单</p>
            <p class="text-xs text-gray-400">点击"落单"按钮添加商品</p>
          </div>

          <div v-else class="space-y-2 max-h-40 overflow-y-auto">
            <div
              v-for="order in orders"
              :key="order.id"
              class="bg-white rounded-lg p-2 border border-gray-200"
            >
              <div class="flex items-center justify-between mb-1">
                <span class="text-xs text-gray-500">{{
                  formatOrderTime(order.createdAt)
                }}</span>
                <span
                  :class="[
                    'px-2 py-0.5 rounded-full text-xs font-medium',
                    getOrderStatusClass(order.status)
                  ]"
                >
                  {{ getOrderStatusText(order.status) }}
                </span>
              </div>
              <div class="space-y-1">
                <div
                  v-for="item in order.items"
                  :key="item.productId"
                  class="flex justify-between text-sm"
                >
                  <span>{{ item.product.name }} × {{ item.quantity }}</span>
                  <span>¥{{ item.subtotal }}</span>
                </div>
              </div>
              <div
                class="flex justify-between items-center mt-2 pt-2 border-t border-gray-100"
              >
                <span class="text-sm font-medium">订单总计</span>
                <span class="font-bold text-blue-600"
                  >¥{{ order.totalAmount }}</span
                >
              </div>
            </div>
          </div>

          <div
            v-if="orders.length > 0"
            class="mt-3 pt-3 border-t border-gray-200"
          >
            <div class="flex justify-between items-center">
              <span class="text-sm font-medium text-gray-800"
                >房间消费总计</span
              >
              <span class="font-bold text-orange-600">¥{{ totalAmount }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex-grow flex flex-col justify-end">
        <!-- 空闲状态：显示一个居中的开房按钮 -->
        <div
          v-if="selectedRoom.status === 'free'"
          class="flex items-center justify-center h-full"
        >
          <button
            :class="[
              'w-4/5 py-4 text-xl font-bold rounded-xl transition-all duration-300 transform hover:scale-105',
              getActionButtonClass('primary'),
              'shadow-lg hover:shadow-2xl'
            ]"
            @click="emit('roomAction', 'open-room', selectedRoom)"
          >
            <font-awesome-icon icon="door-open" class="mr-3" />
            开 房
          </button>
        </div>

        <!-- 其他状态：正常显示按钮 -->
        <div v-else class="grid grid-cols-3 gap-2">
          <button
            v-for="action in getRoomActions(selectedRoom.status)"
            :key="action.key"
            :class="[
              'flex items-center justify-center px-2 py-2.5 rounded-lg font-medium transition-all duration-200 text-sm',
              getActionButtonClass(action.type),
              {
                'col-span-3 !py-3 text-base': action.key === 'checkout'
              }
            ]"
            @click="emit('roomAction', action.key, selectedRoom)"
          >
            <font-awesome-icon :icon="action.icon" class="mr-2" />
            {{ action.label }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Room, RoomStatus } from "../../types/room";
import type { Order } from "../../types/product";
import { getStatusText, getRoomActions } from "../../utils/room-helpers";

defineOptions({
  name: "RoomDetailPanel"
});

// Props
interface Props {
  selectedRoom: Room | null;
  orders: Order[];
  totalAmount: number;
}

defineProps<Props>();

// Emits
const emit = defineEmits<{
  roomAction: [action: string, room: Room];
}>();

// 获取状态标签样式
const getStatusTagClass = (status: RoomStatus): string => {
  const tagClasses: Record<RoomStatus, string> = {
    occupied: "bg-orange-100 text-orange-800",
    free: "bg-green-100 text-green-800",
    cleaning: "bg-red-100 text-red-800",
    booked: "bg-blue-100 text-blue-800",
    maintenance: "bg-purple-100 text-purple-800",
    disabled: "bg-gray-100 text-gray-800",
    checkout: "bg-yellow-100 text-yellow-800",
    transfer: "bg-indigo-100 text-indigo-800",
    overtime: "bg-red-200 text-red-900",
    "vip-service": "bg-pink-100 text-pink-800",
    event: "bg-cyan-100 text-cyan-800",
    reserved: "bg-slate-100 text-slate-800"
  };
  return tagClasses[status] || tagClasses.free;
};

// 获取操作按钮样式
const getActionButtonClass = (type: string): string => {
  const buttonClasses: Record<string, string> = {
    primary: "bg-blue-600 hover:bg-blue-700 text-white shadow-md",
    success: "bg-green-600 hover:bg-green-700 text-white shadow-md",
    danger: "bg-red-600 hover:bg-red-700 text-white shadow-md",
    warning: "bg-orange-600 hover:bg-orange-700 text-white shadow-md",
    "": "bg-slate-100 hover:bg-slate-200 text-slate-700 border border-slate-300"
  };
  return buttonClasses[type] || buttonClasses[""];
};

// 格式化订单时间
const formatOrderTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false
  });
};

// 获取订单状态样式
const getOrderStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    pending: "bg-yellow-100 text-yellow-800",
    completed: "bg-green-100 text-green-800",
    cancelled: "bg-red-100 text-red-800"
  };
  return statusClasses[status] || "bg-gray-100 text-gray-800";
};

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    pending: "待处理",
    completed: "已完成",
    cancelled: "已取消"
  };
  return statusTexts[status] || "未知";
};
</script>

import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { orderApi, productApi } from "@/api/cashier";
import type { Order, OrderForm, Product } from "@/views/cashier/types/product";

/**
 * 订单状态管理（API版本）
 * 使用API服务层与后端交互，替代原有的本地状态管理
 */
export const useOrderApiStore = defineStore("cashier-order-api", () => {
  // 状态
  const orders = ref<Order[]>([]);
  const products = ref<Product[]>([]);
  const isLoading = ref<boolean>(false);
  const error = ref<string | null>(null);

  // 计算属性
  const getOrdersByRoom = computed(() => {
    return (roomId: string) => orders.value.filter(order => order.roomId === roomId);
  });

  const getPendingOrders = computed(() => {
    return orders.value.filter(order => order.status === "pending");
  });

  const getTotalOrderAmount = computed(() => {
    return (roomId: string) => {
      const roomOrders = orders.value.filter(order => order.roomId === roomId);
      return roomOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    };
  });

  // 加载房间订单
  const loadRoomOrders = async (roomId: string) => {
    if (!roomId) return;
    
    isLoading.value = true;
    error.value = null;
    
    try {
      const roomOrders = await orderApi.getOrdersByRoom(roomId);
      
      // 更新或添加新获取的订单
      roomOrders.data.forEach(order => {
        const index = orders.value.findIndex(o => o.id === order.id);
        if (index > -1) {
          // 更新现有订单
          orders.value[index] = order;
        } else {
          // 添加新订单
          orders.value.push(order);
        }
      });
    } catch (err: any) {
      console.error("加载房间订单失败:", err);
      error.value = err.message || "加载订单数据失败";
    } finally {
      isLoading.value = false;
    }
  };

  // 创建订单
  const createOrder = async (orderForm: OrderForm): Promise<Order | null> => {
    isLoading.value = true;
    error.value = null;

    try {
      const newOrder = await orderApi.createOrder(orderForm);
      orders.value.push(newOrder.data);
      return newOrder.data;
    } catch (err: any) {
      console.error("创建订单失败:", err);
      error.value = err.message || "创建订单失败";
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // 更新订单状态
  const updateOrderStatus = async (orderId: string, status: Order["status"]) => {
    isLoading.value = true;
    error.value = null;

    try {
      const updatedOrder = await orderApi.updateOrderStatus(orderId, { status });
      const index = orders.value.findIndex(o => o.id === orderId);
      if (index > -1) {
        orders.value[index] = updatedOrder.data;
      }
      return updatedOrder.data;
    } catch (err: any) {
      console.error("更新订单状态失败:", err);
      error.value = err.message || "更新订单状态失败";
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // 取消订单
  const cancelOrder = async (orderId: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      await orderApi.cancelOrder(orderId);
      const index = orders.value.findIndex(o => o.id === orderId);
      if (index > -1) {
        orders.value[index].status = "cancelled";
      }
      return true;
    } catch (err: any) {
      console.error("取消订单失败:", err);
      error.value = err.message || "取消订单失败";
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  // 房间结账
  const checkoutRoomOrders = async (roomId: string, checkoutData: {
    totalAmount: number;
    notes?: string;
    paymentMethod?: string;
  }) => {
    isLoading.value = true;
    error.value = null;

    try {
      const result = await orderApi.checkoutRoomOrders(roomId, checkoutData);
      if (result.success) {
        // 结账成功后，从本地移除该房间的订单
        orders.value = orders.value.filter(order => order.roomId !== roomId);
      }
      return result;
    } catch (err: any) {
      console.error("房间结账失败:", err);
      error.value = err.message || "房间结账失败";
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  };

  // 加载产品列表
  const loadProducts = async () => {
    isLoading.value = true;
    error.value = null;

    try {
      const result = await productApi.getAllProducts();
      products.value = result.data;
    } catch (err: any) {
      console.error("加载产品数据失败:", err);
      error.value = err.message || "加载产品数据失败";
    } finally {
      isLoading.value = false;
    }
  };

  // 初始化
  const initialize = async () => {
    await loadProducts();
  };

  // 清空房间订单（本地缓存）
  const clearRoomOrders = (roomId: string) => {
    orders.value = orders.value.filter(order => order.roomId !== roomId);
  };

  // 添加订单（别名方法，保持向后兼容）
  const addOrder = (orderForm: OrderForm) => {
    return createOrder(orderForm);
  };

  return {
    // 状态
    orders,
    products,
    isLoading,
    error,

    // 计算属性
    getOrdersByRoom,
    getPendingOrders,
    getTotalOrderAmount,

    // 方法
    loadRoomOrders,
    createOrder,
    addOrder,
    updateOrderStatus,
    cancelOrder,
    checkoutRoomOrders,
    clearRoomOrders,
    loadProducts,
    initialize
  };
});

export const useCashierOrderApi = () => useOrderApiStore();

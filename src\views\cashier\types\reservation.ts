// 预订状态
export type ReservationStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'no_show';

// 预订类型
export interface Reservation {
  id: string;
  customerName: string;
  customerPhone: string;
  roomId: string;
  roomType: string;
  reservationDate: string;
  startTime: string;
  endTime: string;
  duration: number; // 小时数
  status: ReservationStatus;
  totalAmount: number;
  deposit: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

// 预订表单数据
export interface ReservationForm {
  customerName: string;
  customerPhone: string;
  roomId: string;
  reservationDate: string;
  startTime: string;
  duration: number;
  notes?: string;
  deposit?: number;
}

// 预订查询参数
export interface ReservationQuery {
  status?: ReservationStatus;
  roomId?: string;
  customerName?: string;
  customerPhone?: string;
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  pageSize?: number;
}

// 预订统计数据
export interface ReservationStats {
  total: number;
  pending: number;
  confirmed: number;
  cancelled: number;
  completed: number;
  no_show: number;
  todayReservations: number;
  tomorrowReservations: number;
}

// 预订日历事件
export interface ReservationCalendarEvent {
  id: string;
  title: string;
  start: string;
  end: string;
  color: string;
  reservation: Reservation;
}

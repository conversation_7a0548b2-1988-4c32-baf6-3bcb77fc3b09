<script setup>
import { ref } from "vue";
import { Search, Upload } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import axios from "axios";
import { onMounted, computed } from "vue";
const stores = ref([]);
const selectedStore = ref(null); // 用于存储选中的门店ID
const tableLoad = ref(false); // 控制表格加载状态
const tableData = ref([]);
// 获取前一天日期
const getTodayRange = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate() - 1).padStart(2, "0");
  const todayStr = `${year}${month}${day}`;
  return [todayStr, todayStr]; // [YYYYMMDD, YYYYMMDD]
};
const select = ref({
  time: getTodayRange(),
  search: ""
});
onMounted(async () => {
  try {
    const response = await axios.get("http://*************:200/ExecUse/Index", {
      params: {
        Ex: "GrouponBase.dbo.Ex_SelTable",
        TableName: "Mims.dbo.ShopInfo"
      }
    });
    let responseData = response.data;
    // console.log("API返回数据:", responseData);
    if (typeof responseData === "string" && responseData.startsWith("(")) {
      responseData = JSON.parse(responseData.replace(/^\((.*)\)$/, "$1"));
    }
    stores.value = responseData.ObjceData.filter(
      shop =>
        shop.IsUse && shop.ShopName !== "清远店" && shop.ShopName !== "黄岐店"
    ) // 只保留 IsUse 为 true 的店铺
      .map(shop => ({
        value: shop.ShopID, // 使用 ShopID 作为 value
        label: shop.ShopName // 使用 ShopName 作为 label
      }));
  } catch (err) {
    console.error("初始化失败:", err);
  }
});

// 搜索
const search = async () => {
  console.log("搜索门店:", selectedStore.value);
  console.log("搜索时间范围:", select.value.time);
  if (select.value.time.length !== 2) {
    ElMessage.error("请选择完整的时间范围！");
    return;
  }
  CurrentPage.value = 1; // 重置为第一页
  await getData();
};

// 查询数据
const getData = async () => {
  tableLoad.value = true;
  const response = await axios.get("http://*************:88/ExecUse/index", {
    params: {
      Ex: "OperateData.dbo.ex_StudentSummary",
      StartDate: select.value.time[0],
      EndDate: select.value.time[1],
      // ShopID: selectedStore.value,
      // Keyword: select.value.search,
      PageSize: PageSize.value,
      PageNumber: CurrentPage.value
    }
  });
  console.log("查询结果:", response);
  let responseData = response.data;
  if (typeof responseData === "string" && responseData.startsWith("(")) {
    responseData = JSON.parse(responseData.replace(/^\((.*)\)$/, "$1"));
  }
  console.log(responseData);
  tableData.value = responseData.ObjceData || []; // 确保是数组类型，避免空值问题
  totalCount.value = responseData.ObjceData[0]
    ? responseData.ObjceData[0].TotalCount
    : 0; // 总记录数
  tableLoad.value = false;
  if (tableData.value.length === 0) {
    ElMessage("当前选择的日期没有查询到数据！");
  }
};

// 导出
const isExporting = ref(false); // 控制导出按钮的加载状态
const derived = async () => {
  // console.log("导出门店:", selectedStore.value);
  // console.log("导出时间范围:", select.value.time);
  if (select.value.time.length !== 2) {
    ElMessage.error("请选择完整的时间范围！");
    return;
  }
  isExporting.value = true;
  try {
    const response = await axios.get(
      "http://*************:88/ExportToCsv/export",
      {
        params: {
          Ex: "OperateData.dbo.ex_StudentSummaryExport",
          // ShopId: selectedStore.value,
          StartDate: select.value.time[0],
          EndDate: select.value.time[1]
          // Keyword: select.value.search || ""
        },
        responseType: "blob", // 必须
        timeout: 10000
      }
    );

    // 处理文件名
    // let fileName = "export.csv";
    const selectedOption = stores.value.find(
      option => option.value === selectedStore.value
    );
    let fileName = `大学生优惠统计汇总报表_${select.value.time[0]}-${select.value.time[1]}.csv`;
    const disposition = response.headers["content-disposition"];
    if (disposition) {
      const match = disposition.match(/filename="?(.+)"?/);
      // if (match) fileName = match[1];
    }
    // 触发下载
    const blob = new Blob([response.data]);
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    isExporting.value = false;
  } catch (error) {
    isExporting.value = false;
    console.error("导出失败:", error);
    ElMessage.error(`导出失败: ${error.message}`);
  }
};

// 创建门店名称映射
const shopNameMap = computed(() => {
  const map = {};
  stores.value.forEach(store => {
    map[store.value] = store.label;
  });
  return map;
});

// 分页
const CurrentPage = ref(1); // 当前页码
const PageSize = ref(20); // 每页显示条数
const totalCount = ref(0); // 总数据量
const handleCurrentChange = async page => {
  CurrentPage.value = page;
  await getData();
};
const handleSizeChange = async size => {
  PageSize.value = size;
  CurrentPage.value = 1; // 重置为第一页
  await getData();
};
const disabledDate = time => {
  // 计算 6 个月前的日期
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
  const now = new Date();
  now.setDate(now.getDate() - 1); // 设置时间为月末的最后一天（可选，根据需求调整）
  // 禁用 6 个月前的日期
  return time < sixMonthsAgo || time > now;
};
</script>

<template>
  <div>
    <div class="ml-2 mt-2">
      <el-date-picker
        v-model="select.time"
        type="daterange"
        start-placeholder="开始时间日期"
        end-placeholder="结束时间日期"
        format="YYYY-MM-DD"
        value-format="YYYYMMDD"
        :disabled-date="disabledDate"
      />
      <el-button class="ml-2" type="primary" @click="search">
        <el-icon>
          <Search />
        </el-icon>
        查询
      </el-button>
      <el-button type="success" @click="derived" :loading="isExporting">
        <el-icon><Upload /></el-icon>
        导出
      </el-button>
    </div>

    <!-- 表格数据 -->
    <el-table
      :data="tableData"
      border
      style="width: 99%; margin: 10px auto; height: 66vh"
      v-loading="tableLoad"
      :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
      :cell-style="{ textAlign: 'center' }"
    >
      <el-table-column prop="OpenId" label="大学生编码" />

      <el-table-column prop="tel" label="手机" />
      <el-table-column prop="UseSummary" label="累计优惠次数" />
      <el-table-column prop="IsMember" label="是否会员" />
      <el-table-column prop="PurchaseTime" label="首次消费时间" />

      <!-- <el-table-column prop="name" label="开房手机号" width="180" /> -->
    </el-table>
    <el-pagination
      v-model:current-page="CurrentPage"
      v-model:page-size="PageSize"
      :page-sizes="[20, 50, 100, 200]"
      layout="total,sizes, prev, pager, next"
      :total="totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-left: 20px"
    />
  </div>
</template>

<style lang="scss" scoped>
.header {
  font: 100 15px "微软雅黑";
  display: flex;
  align-items: center;
  padding-top: 5px;
}
.store-list {
  display: flex;
  flex-direction: row;
  margin-left: 5px;
  margin-bottom: 5px;
}
.store-item {
  margin-right: 10px;
  padding: 5px;
  background-color: #249672;
  color: white;
  cursor: pointer;
  margin-top: 5px;
}
.store-item:hover {
  background-color: #ff7f50;
}

.store-item.active {
  background-color: #ff7f50;
}
.search-input {
  width: 300px;
  margin: 10px 10px;
}
</style>

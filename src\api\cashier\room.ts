import { cashierHttp } from "./http";
import type { CashierApiResponse, CashierPaginatedResponse } from "./types";
import type { Room, RoomStatus } from "@/views/cashier/types/room";

/**
 * 收银系统房间API
 * 处理所有与房间相关的API请求
 */

// 房间查询参数
export interface RoomQueryParams {
  area?: string;
  status?: RoomStatus;
  page?: number;
  pageSize?: number;
  keyword?: string;
}

// 房间操作参数
export interface RoomOperationParams {
  customer?: string;
  notes?: string;
  duration?: number;
  amount?: number;
}

// 房间状态更新参数
export interface RoomStatusUpdateParams {
  status: RoomStatus;
  reason?: string;
  operator?: string;
}

/**
 * 获取所有房间
 */
export const getAllRooms = (params?: RoomQueryParams): Promise<CashierApiResponse<Room[]>> => {
  return cashierHttp.get<CashierApiResponse<Room[]>>("/rooms", { params });
};

/**
 * 分页获取房间列表
 */
export const getRoomsPaginated = (params: RoomQueryParams): Promise<CashierPaginatedResponse<Room>> => {
  return cashierHttp.get<CashierPaginatedResponse<Room>>("/rooms/paginated", { params });
};

/**
 * 获取房间详情
 */
export const getRoomById = (roomId: string): Promise<CashierApiResponse<Room>> => {
  return cashierHttp.get<CashierApiResponse<Room>>(`/rooms/${roomId}`);
};

/**
 * 按区域获取房间
 */
export const getRoomsByArea = (area: string): Promise<CashierApiResponse<Room[]>> => {
  return cashierHttp.get<CashierApiResponse<Room[]>>(`/rooms/area/${area}`);
};

/**
 * 按状态获取房间
 */
export const getRoomsByStatus = (status: RoomStatus): Promise<CashierApiResponse<Room[]>> => {
  return cashierHttp.get<CashierApiResponse<Room[]>>(`/rooms/status/${status}`);
};

/**
 * 更新房间状态
 */
export const updateRoomStatus = (
  roomId: string, 
  params: RoomStatusUpdateParams
): Promise<CashierApiResponse<Room>> => {
  return cashierHttp.patch<CashierApiResponse<Room>>(`/rooms/${roomId}/status`, { data: params });
};

/**
 * 开房操作
 */
export const openRoom = (
  roomId: string, 
  params: RoomOperationParams
): Promise<CashierApiResponse<Room>> => {
  return cashierHttp.post<CashierApiResponse<Room>>(`/rooms/${roomId}/open`, { data: params });
};

/**
 * 结账操作
 */
export const checkoutRoom = (
  roomId: string, 
  params: RoomOperationParams
): Promise<CashierApiResponse<Room>> => {
  return cashierHttp.post<CashierApiResponse<Room>>(`/rooms/${roomId}/checkout`, { data: params });
};

/**
 * 换房操作
 */
export const transferRoom = (
  fromRoomId: string,
  toRoomId: string,
  params?: RoomOperationParams
): Promise<CashierApiResponse<{ fromRoom: Room; toRoom: Room }>> => {
  return cashierHttp.post<CashierApiResponse<{ fromRoom: Room; toRoom: Room }>>(
    `/rooms/${fromRoomId}/transfer/${toRoomId}`, 
    { data: params }
  );
};

/**
 * 预订房间
 */
export const bookRoom = (
  roomId: string,
  params: RoomOperationParams & {
    reservationTime: string;
    contactPhone: string;
  }
): Promise<CashierApiResponse<Room>> => {
  return cashierHttp.post<CashierApiResponse<Room>>(`/rooms/${roomId}/book`, { data: params });
};

/**
 * 取消预订
 */
export const cancelBooking = (
  roomId: string,
  params?: { reason?: string }
): Promise<CashierApiResponse<Room>> => {
  return cashierHttp.post<CashierApiResponse<Room>>(`/rooms/${roomId}/cancel-booking`, { data: params });
};

/**
 * 清洁完成
 */
export const markCleaningDone = (
  roomId: string,
  params?: { operator?: string; notes?: string }
): Promise<CashierApiResponse<Room>> => {
  return cashierHttp.post<CashierApiResponse<Room>>(`/rooms/${roomId}/clean-done`, { data: params });
};

/**
 * 维修完成
 */
export const markMaintenanceDone = (
  roomId: string,
  params?: { operator?: string; notes?: string }
): Promise<CashierApiResponse<Room>> => {
  return cashierHttp.post<CashierApiResponse<Room>>(`/rooms/${roomId}/maintenance-done`, { data: params });
};

/**
 * 启用房间
 */
export const enableRoom = (
  roomId: string,
  params?: { operator?: string; notes?: string }
): Promise<CashierApiResponse<Room>> => {
  return cashierHttp.post<CashierApiResponse<Room>>(`/rooms/${roomId}/enable`, { data: params });
};

/**
 * 禁用房间
 */
export const disableRoom = (
  roomId: string,
  params: { reason: string; operator?: string }
): Promise<CashierApiResponse<Room>> => {
  return cashierHttp.post<CashierApiResponse<Room>>(`/rooms/${roomId}/disable`, { data: params });
};

/**
 * 获取房间使用统计
 */
export const getRoomStatistics = (params?: {
  startDate?: string;
  endDate?: string;
  area?: string;
}): Promise<CashierApiResponse<{
  totalRooms: number;
  occupiedRooms: number;
  availableRooms: number;
  maintenanceRooms: number;
  occupancyRate: number;
  revenueToday: number;
}>> => {
  return cashierHttp.get<CashierApiResponse<any>>("/rooms/statistics", { params });
};

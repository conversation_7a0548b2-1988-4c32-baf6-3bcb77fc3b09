import { http } from "@/utils/http";
import { baseUrlApi } from "./util";

export interface BankDealDto {
  dealSK: number;
  bankSK: number;
  fdNo?: string;
  dealName?: string;
  dealAmount?: number;
  subsidyAmount?: number;
  totalAmount?: number;
  serviceFee?: number;
  netAmount?: number;
}

export interface BankDealQuery {
  page?: number;
  rows?: number;
  sidx?: string;
  sord?: "asc" | "desc";
  bankSk?: number;
  fdNo?: string;
  dealName?: string;
}

export interface PageResult<T> {
  items: T[];
  total: number;
}

export interface ApiResponse<T> {
  state: number;
  message: string;
  data: T;
  total?: number;
}

export const listBankDeals = (params: BankDealQuery = {}) => {
  return http.get<ApiResponse<PageResult<BankDealDto>>, any>(baseUrlApi("BankDeals"), {
    params
  });
};

export const getBankDealById = (id: number) => {
  return http.get<ApiResponse<BankDealDto>, any>(`${baseUrlApi("BankDeals")}/${id}`);
};

export const createBankDeal = (data: Omit<BankDealDto, "dealSK">) => {
  return http.post<ApiResponse<BankDealDto>, typeof data>(baseUrlApi("BankDeals"), {
    data
  });
};

export const updateBankDeal = (id: number, data: Partial<Omit<BankDealDto, "dealSK">>) => {
  return http.request<ApiResponse<BankDealDto>>("put", `${baseUrlApi("BankDeals")}/${id}`, {
    data
  });
};

export const deleteBankDeal = (id: number) => {
  return http.request<ApiResponse<boolean>>("delete", `${baseUrlApi("BankDeals")}/${id}`);
};

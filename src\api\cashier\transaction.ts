import { cashierHttp } from "./http";
import type { CashierApiResponse, CashierPaginatedResponse } from "./types";

/**
 * 收银系统交易API
 * 处理所有与交易相关的API请求
 */

// 交易类型
export type TransactionType = 
  | "checkin"      // 开房
  | "checkout"     // 结账
  | "order"        // 下单
  | "refund"       // 退款
  | "transfer"     // 换房
  | "deposit"      // 押金
  | "adjustment";  // 调整

// 支付方式
export type PaymentMethod = 
  | "cash"         // 现金
  | "card"         // 银行卡
  | "alipay"       // 支付宝
  | "wechat"       // 微信支付
  | "member"       // 会员卡
  | "voucher"      // 代金券
  | "mixed";       // 混合支付

// 交易状态
export type TransactionStatus = 
  | "pending"      // 待处理
  | "processing"   // 处理中
  | "completed"    // 已完成
  | "failed"       // 失败
  | "cancelled"    // 已取消
  | "refunded";    // 已退款

// 交易信息
export interface Transaction {
  id: string;
  roomId: string;
  type: TransactionType;
  status: TransactionStatus;
  amount: number;
  paymentMethod: PaymentMethod;
  description: string;
  operator: string;
  timestamp: string;
  orderId?: string;
  customerId?: string;
  notes?: string;
  refundInfo?: {
    refundId: string;
    refundAmount: number;
    refundReason: string;
    refundTime: string;
  };
}

// 交易查询参数
export interface TransactionQueryParams {
  roomId?: string;
  type?: TransactionType;
  status?: TransactionStatus;
  paymentMethod?: PaymentMethod;
  startDate?: string;
  endDate?: string;
  operator?: string;
  page?: number;
  pageSize?: number;
  keyword?: string;
}

// 创建交易参数
export interface CreateTransactionParams {
  roomId: string;
  type: TransactionType;
  amount: number;
  paymentMethod: PaymentMethod;
  description: string;
  orderId?: string;
  customerId?: string;
  notes?: string;
}

// 支付参数
export interface PaymentParams {
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDetails?: {
    cardNumber?: string;
    transactionId?: string;
    voucher?: {
      code: string;
      amount: number;
    };
    mixed?: Array<{
      method: PaymentMethod;
      amount: number;
      details?: any;
    }>;
  };
}

// 退款参数
export interface RefundParams {
  amount: number;
  reason: string;
  operator: string;
  notes?: string;
}

/**
 * 创建交易
 */
export const createTransaction = (params: CreateTransactionParams): Promise<CashierApiResponse<Transaction>> => {
  return cashierHttp.post<CashierApiResponse<Transaction>>("/transactions", { data: params });
};

/**
 * 获取交易详情
 */
export const getTransactionById = (transactionId: string): Promise<CashierApiResponse<Transaction>> => {
  return cashierHttp.get<CashierApiResponse<Transaction>>(`/transactions/${transactionId}`);
};

/**
 * 分页获取交易列表
 */
export const getTransactionsPaginated = (params: TransactionQueryParams): Promise<CashierPaginatedResponse<Transaction>> => {
  return cashierHttp.get<CashierPaginatedResponse<Transaction>>("/transactions/paginated", { params });
};

/**
 * 获取房间交易记录
 */
export const getRoomTransactions = (roomId: string): Promise<CashierApiResponse<Transaction[]>> => {
  return cashierHttp.get<CashierApiResponse<Transaction[]>>(`/transactions/room/${roomId}`);
};

/**
 * 获取今日交易记录
 */
export const getTodayTransactions = (): Promise<CashierApiResponse<Transaction[]>> => {
  return cashierHttp.get<CashierApiResponse<Transaction[]>>("/transactions/today");
};

/**
 * 按类型获取交易记录
 */
export const getTransactionsByType = (type: TransactionType): Promise<CashierApiResponse<Transaction[]>> => {
  return cashierHttp.get<CashierApiResponse<Transaction[]>>(`/transactions/type/${type}`);
};

/**
 * 按状态获取交易记录
 */
export const getTransactionsByStatus = (status: TransactionStatus): Promise<CashierApiResponse<Transaction[]>> => {
  return cashierHttp.get<CashierApiResponse<Transaction[]>>(`/transactions/status/${status}`);
};

/**
 * 处理支付
 */
export const processPayment = (
  transactionId: string,
  params: PaymentParams
): Promise<CashierApiResponse<{
  success: boolean;
  transactionId: string;
  paymentId: string;
  message: string;
}>> => {
  return cashierHttp.post<CashierApiResponse<any>>(`/transactions/${transactionId}/payment`, { data: params });
};

/**
 * 确认交易
 */
export const confirmTransaction = (
  transactionId: string,
  params?: { notes?: string }
): Promise<CashierApiResponse<Transaction>> => {
  return cashierHttp.post<CashierApiResponse<Transaction>>(`/transactions/${transactionId}/confirm`, { data: params });
};

/**
 * 取消交易
 */
export const cancelTransaction = (
  transactionId: string,
  reason: string
): Promise<CashierApiResponse<Transaction>> => {
  return cashierHttp.post<CashierApiResponse<Transaction>>(`/transactions/${transactionId}/cancel`, {
    data: { reason }
  });
};

/**
 * 申请退款
 */
export const requestRefund = (
  transactionId: string,
  params: RefundParams
): Promise<CashierApiResponse<{
  refundId: string;
  refundAmount: number;
  status: string;
  message: string;
}>> => {
  return cashierHttp.post<CashierApiResponse<any>>(`/transactions/${transactionId}/refund`, { data: params });
};

/**
 * 获取交易统计信息
 */
export const getTransactionStatistics = (params?: {
  startDate?: string;
  endDate?: string;
  operator?: string;
}): Promise<CashierApiResponse<{
  totalTransactions: number;
  totalAmount: number;
  completedTransactions: number;
  completedAmount: number;
  refundTransactions: number;
  refundAmount: number;
  paymentMethodStats: Array<{
    method: PaymentMethod;
    count: number;
    amount: number;
  }>;
  hourlyStats: Array<{
    hour: number;
    count: number;
    amount: number;
  }>;
}>> => {
  return cashierHttp.get<CashierApiResponse<any>>("/transactions/statistics", { params });
};

/**
 * 获取收银员交易统计
 */
export const getCashierStatistics = (params?: {
  startDate?: string;
  endDate?: string;
}): Promise<CashierApiResponse<{
  operator: string;
  totalTransactions: number;
  totalAmount: number;
  averageTransactionValue: number;
  transactionTypes: Array<{
    type: TransactionType;
    count: number;
    amount: number;
  }>;
}>> => {
  return cashierHttp.get<CashierApiResponse<any>>("/transactions/cashier-statistics", { params });
};

/**
 * 搜索交易记录
 */
export const searchTransactions = (params: {
  keyword: string;
  searchType?: "transactionId" | "roomId" | "operator" | "description" | "all";
  page?: number;
  pageSize?: number;
}): Promise<CashierPaginatedResponse<Transaction>> => {
  return cashierHttp.get<CashierPaginatedResponse<Transaction>>("/transactions/search", { params });
};

/**
 * 导出交易记录
 */
export const exportTransactions = (params: TransactionQueryParams & {
  format?: "excel" | "csv" | "pdf";
}): Promise<CashierApiResponse<{
  downloadUrl: string;
  fileName: string;
  expiresAt: string;
}>> => {
  return cashierHttp.post<CashierApiResponse<any>>("/transactions/export", { data: params });
};

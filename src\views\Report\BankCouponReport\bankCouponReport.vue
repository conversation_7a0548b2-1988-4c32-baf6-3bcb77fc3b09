<template>
  <div class="page-wrapper">
    <el-card class="mb-4" shadow="never">
      <div class="filter-form">
        <el-form :inline="true" :model="query" label-width="90px">
          <el-form-item label="银行">
            <el-select
              v-model="query.bankSKs"
              placeholder="选择银行（可多选）"
              clearable
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              class="w-60"
            >
              <el-option
                v-for="b in banks"
                :key="b.bankSK"
                :label="b.bankName"
                :value="b.bankSK"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="团购券编号">
            <el-input
              v-model.trim="query.fdNo"
              placeholder="请输入团购券编号"
              clearable
              class="w-60"
            />
          </el-form-item>
          <el-form-item label="团购券名称">
            <el-input
              v-model.trim="query.dealName"
              placeholder="请输入团购券名称"
              clearable
              class="w-60"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card shadow="never">
      <div class="toolbar mb-3">
        <el-button type="primary" @click="openBankDialog()">新增银行</el-button>
        <el-button type="success" @click="openDealDialog()"
          >新增团购券</el-button
        >
      </div>

      <el-table
        :data="tableData"
        border
        stripe
        @sort-change="onSortChange"
        :height="tableHeight"
      >
        <el-table-column
          prop="bankSK"
          label="银行"
          width="140"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ bankNameMap.get(row.bankSK) ?? row.bankSK }}
          </template>
        </el-table-column>
        <el-table-column
          prop="fdNo"
          label="团购券编号"
          min-width="140"
          sortable="custom"
        />
        <el-table-column
          prop="dealName"
          label="团购券名称"
          min-width="160"
          sortable="custom"
        />
        <el-table-column
          prop="dealAmount"
          label="团购金额"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">{{
            formatCurrency(row.dealAmount)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="subsidyAmount"
          label="补贴金额"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">{{
            formatCurrency(row.subsidyAmount)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="totalAmount"
          label="总金额"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">{{
            formatCurrency(row.totalAmount)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="serviceFee"
          label="服务费"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">{{
            formatCurrency(row.serviceFee)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="netAmount"
          label="实收金额"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">{{
            formatCurrency(row.netAmount)
          }}</template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              size="small"
              @click="openDealDialog(row)"
              >编辑</el-button
            >
            <el-popconfirm
              title="确定删除该团购券吗？"
              @confirm="onDeleteDeal(row.dealSK)"
            >
              <template #reference>
                <el-button link type="danger" size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <div class="mt-3 flex justify-end">
        <el-pagination
          v-model:current-page="pager.page"
          v-model:page-size="pager.rows"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadData"
          @current-change="loadData"
        />
      </div>
    </el-card>

    <!-- 银行弹窗 -->
    <el-dialog
      v-model="bankDialog.visible"
      title="银行"
      width="420px"
      @closed="resetBankForm"
    >
      <el-form
        :model="bankDialog.form"
        :rules="bankRules"
        ref="bankFormRef"
        label-width="80px"
      >
        <el-form-item label="银行名称" prop="bankName">
          <el-input v-model.trim="bankDialog.form.bankName" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="bankDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="bankDialog.loading"
          @click="saveBank"
          >保存</el-button
        >
      </template>
    </el-dialog>

    <!-- 券弹窗 -->
    <el-dialog
      v-model="dealDialog.visible"
      :title="dealDialog.form.dealSK ? '编辑团购券' : '新增团购券'"
      width="680px"
      @closed="resetDealForm"
    >
      <el-form
        :model="dealDialog.form"
        :rules="dealRules"
        ref="dealFormRef"
        label-width="100px"
      >
        <el-form-item label="银行" prop="bankSK">
          <el-select
            v-model="dealDialog.form.bankSK"
            placeholder="选择银行"
            class="w-60"
          >
            <el-option
              v-for="b in banks"
              :key="b.bankSK"
              :label="b.bankName"
              :value="b.bankSK"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="fdNo">
          <template #label> 团购券编号 </template>
          <el-input
            v-model.trim="dealDialog.form.fdNo"
            placeholder="团购券编号选填"
          />
        </el-form-item>
        <el-form-item label="团购券名称" prop="dealName">
          <el-input v-model.trim="dealDialog.form.dealName" />
        </el-form-item>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="团购金额" prop="dealAmount">
              <el-input-number
                v-model="dealDialog.form.dealAmount"
                :min="0"
                :step="1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补贴金额" prop="subsidyAmount">
              <el-input-number
                v-model="dealDialog.form.subsidyAmount"
                :min="0"
                :step="1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="服务费" prop="serviceFee">
              <el-input-number
                v-model="dealDialog.form.serviceFee"
                :min="0"
                :step="1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="dealDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="dealDialog.loading"
          @click="saveDeal"
          >保存</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { fetchAllBanks, createBank, BankDto } from "@/api/bank";
import {
  listBankDeals,
  createBankDeal,
  updateBankDeal,
  deleteBankDeal,
  BankDealDto
} from "@/api/bankDeal";

const tableHeight = computed(() => window.innerHeight - 360);

// 查询参数/分页
const query = reactive({
  bankSKs: [] as number[],
  fdNo: "",
  dealName: "",
  sidx: "dealSK",
  sord: "desc" as "asc" | "desc"
});
const pager = reactive({ page: 1, rows: 20 });
const total = ref(0);

// 数据源
const banks = ref<BankDto[]>([]);
const bankNameMap = computed(
  () => new Map(banks.value.map(b => [b.bankSK, b.bankName]))
);
const tableData = ref<BankDealDto[]>([]);

// 加载银行与表格
const loadBanks = async () => {
  try {
    banks.value = await fetchAllBanks();
  } catch (e) {
    // 错误已在拦截器统一提示
  }
};

const loadData = async () => {
  try {
    const params = { ...query, page: pager.page, rows: pager.rows } as any;
    const res: any = await listBankDeals(params);
    const data = res?.data;
    // 兼容结构：data.rows/total | data.items/total | data 为数组
    const rows: any[] =
      data?.rows ?? data?.items ?? (Array.isArray(data) ? data : []);
    // 字段名映射（PascalCase -> camelCase）
    tableData.value = rows.map((r: any) => ({
      dealSK: r.dealSK ?? r.DealSK,
      bankSK: r.bankSK ?? r.BankSK,
      fdNo: r.fdNo ?? r.FdNo,
      dealName: r.dealName ?? r.DealName,
      dealAmount: r.dealAmount ?? r.DealAmount,
      subsidyAmount: r.subsidyAmount ?? r.SubsidyAmount,
      totalAmount: r.totalAmount ?? r.TotalAmount,
      serviceFee: r.serviceFee ?? r.ServiceFee,
      netAmount: r.netAmount ?? r.NetAmount
    }));
    total.value = (data?.total ?? res?.total ?? rows.length ?? 0) as number;
  } catch (e) {
    tableData.value = [];
    total.value = 0;
  }
};

const onSearch = () => {
  pager.page = 1;
  loadData();
};
const onReset = () => {
  query.bankSKs = [];
  query.fdNo = "";
  query.dealName = "";
  pager.page = 1;
  query.sidx = "dealSK";
  query.sord = "desc";
  loadData();
};

function onSortChange({ prop, order }: any) {
  if (!prop) return;
  query.sidx = prop;
  query.sord = order === "ascending" ? "asc" : "desc";
  loadData();
}

function formatCurrency(v?: number) {
  if (v == null) return "-";
  return Number(v).toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

// 银行弹窗
const bankDialog = reactive({
  visible: false,
  loading: false,
  form: { bankName: "" }
});
const bankFormRef = ref<FormInstance>();
const bankRules: FormRules = {
  bankName: [{ required: true, message: "请输入银行名称", trigger: "blur" }]
};
function openBankDialog() {
  bankDialog.visible = true;
}
function resetBankForm() {
  bankDialog.loading = false;
  bankDialog.form.bankName = "";
}
async function saveBank() {
  await bankFormRef.value?.validate();
  bankDialog.loading = true;
  try {
    await createBank({ bankName: bankDialog.form.bankName });
    ElMessage.success("保存成功");
    bankDialog.visible = false;
    loadBanks();
  } finally {
    bankDialog.loading = false;
  }
}

// 券弹窗
const dealDialog = reactive<{
  visible: boolean;
  loading: boolean;
  form: Partial<BankDealDto>;
}>({ visible: false, loading: false, form: {} });
const dealFormRef = ref<FormInstance>();
const dealRules: FormRules = {
  bankSK: [{ required: true, message: "请选择银行", trigger: "change" }],
  // fdNo 非必填
  dealName: [{ required: true, message: "请输入团购券名称", trigger: "blur" }]
};
function openDealDialog(row?: BankDealDto) {
  dealDialog.form = row
    ? { ...row }
    : { bankSK: query.bankSKs.length === 1 ? query.bankSKs[0] : undefined };
  dealDialog.visible = true;
}
function resetDealForm() {
  dealDialog.loading = false;
  dealDialog.form = {};
}
async function saveDeal() {
  await dealFormRef.value?.validate();
  dealDialog.loading = true;
  try {
    const f = dealDialog.form;
    if (f.dealSK) {
      await updateBankDeal(f.dealSK, {
        bankSK: f.bankSK!,
        fdNo: f.fdNo,
        dealName: f.dealName,
        dealAmount: f.dealAmount,
        subsidyAmount: f.subsidyAmount,
        serviceFee: f.serviceFee
      });
    } else {
      await createBankDeal({
        bankSK: f.bankSK!,
        fdNo: f.fdNo,
        dealName: f.dealName,
        dealAmount: f.dealAmount,
        subsidyAmount: f.subsidyAmount,
        serviceFee: f.serviceFee
      });
    }
    ElMessage.success("保存成功");
    dealDialog.visible = false;
    loadData();
  } finally {
    dealDialog.loading = false;
  }
}

async function onDeleteDeal(id: number) {
  await deleteBankDeal(id);
  ElMessage.success("删除成功");
  loadData();
}

onMounted(async () => {
  await loadBanks();
  await loadData();
});
</script>

<style scoped>
.page-wrapper {
  padding: 12px;
  background: #fff; /* 页面白色背景 */
  min-height: 100vh; /* 白色背景随内容拉伸，覆盖整屏 */
}
.filter-form .w-60 {
  width: 240px;
}
.w-full {
  width: 100%;
}
.mb-3 {
  margin-bottom: 12px;
}
.mb-4 {
  margin-bottom: 16px;
}
</style>

<template>
  <div class="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-slate-200">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
            <font-awesome-icon icon="history" class="text-white" />
          </div>
          <div>
            <h2 class="text-xl font-bold text-slate-800">消费记录</h2>
            <p class="text-sm text-slate-500">{{ customer?.name }} 的消费历史</p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="w-8 h-8 flex items-center justify-center text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
        >
          <font-awesome-icon icon="times" />
        </button>
      </div>

      <!-- 统计概览 -->
      <div class="p-6 border-b border-slate-100">
        <div class="grid grid-cols-4 gap-4">
          <div class="bg-blue-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-600">{{ totalRecords }}</div>
            <div class="text-sm text-slate-600">消费次数</div>
          </div>
          <div class="bg-green-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600">¥{{ totalAmount }}</div>
            <div class="text-sm text-slate-600">总消费金额</div>
          </div>
          <div class="bg-purple-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-purple-600">¥{{ averageAmount }}</div>
            <div class="text-sm text-slate-600">平均消费</div>
          </div>
          <div class="bg-orange-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-orange-600">{{ totalHours }}</div>
            <div class="text-sm text-slate-600">总消费时长(小时)</div>
          </div>
        </div>
      </div>

      <!-- 筛选工具栏 -->
      <div class="flex items-center justify-between p-6 border-b border-slate-100">
        <div class="flex items-center space-x-4">
          <!-- 日期筛选 -->
          <div class="flex items-center space-x-2">
            <input
              v-model="dateFrom"
              type="date"
              class="px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            />
            <span class="text-slate-500">至</span>
            <input
              v-model="dateTo"
              type="date"
              class="px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            />
          </div>

          <!-- 房间筛选 -->
          <select
            v-model="roomFilter"
            class="px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <option value="">全部房间</option>
            <option value="VIP">VIP包厢</option>
            <option value="中包厢">中包厢</option>
            <option value="小包厢">小包厢</option>
          </select>
        </div>

        <button
          @click="exportRecords"
          class="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
        >
          <font-awesome-icon icon="download" class="mr-2" />
          导出记录
        </button>
      </div>

      <!-- 消费记录列表 -->
      <div class="flex-1 overflow-auto p-6">
        <div class="space-y-4">
          <div
            v-for="record in filteredRecords"
            :key="record.id"
            class="bg-white border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <span class="text-green-600 font-bold">{{ record.roomId }}</span>
                </div>
                <div>
                  <h3 class="font-semibold text-slate-800">{{ record.roomType }}</h3>
                  <p class="text-sm text-slate-500">
                    {{ formatDateTime(record.startTime) }} - {{ formatTime(record.endTime) }}
                  </p>
                  <p class="text-sm text-slate-600">
                    时长: {{ record.duration }}小时 | 收银员: {{ record.cashierName }}
                  </p>
                </div>
              </div>
              
              <div class="text-right">
                <div class="text-lg font-bold text-slate-800">¥{{ record.totalAmount }}</div>
                <div class="text-sm text-slate-500">
                  <span v-if="record.discountAmount > 0" class="text-red-500">
                    优惠: -¥{{ record.discountAmount }}
                  </span>
                </div>
                <div class="text-sm text-slate-600">
                  <span v-if="record.pointsEarned > 0" class="text-blue-500">
                    +{{ record.pointsEarned }}积分
                  </span>
                  <span v-if="record.pointsUsed > 0" class="text-orange-500 ml-2">
                    -{{ record.pointsUsed }}积分
                  </span>
                </div>
              </div>
            </div>
            
            <div v-if="record.notes" class="mt-3 pt-3 border-t border-slate-100">
              <p class="text-sm text-slate-600">备注: {{ record.notes }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="flex items-center justify-between p-6 border-t border-slate-200">
        <span class="text-sm text-slate-500">
          共 {{ totalRecords }} 条消费记录
        </span>
        <div class="flex space-x-2">
          <button
            :disabled="currentPage === 1"
            @click="currentPage--"
            class="px-3 py-1 border border-slate-300 rounded disabled:opacity-50"
          >
            上一页
          </button>
          <span class="px-3 py-1">{{ currentPage }} / {{ totalPages }}</span>
          <button
            :disabled="currentPage === totalPages"
            @click="currentPage++"
            class="px-3 py-1 border border-slate-300 rounded disabled:opacity-50"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { Customer, ConsumptionRecord } from '../../types/customer';

defineOptions({
  name: 'ConsumptionHistory'
});

// Props
const props = defineProps<{
  customer: Customer | null;
}>();

// Emits
const emit = defineEmits<{
  close: [];
}>();

// 响应式数据
const records = ref<ConsumptionRecord[]>([]);
const dateFrom = ref('');
const dateTo = ref('');
const roomFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(10);

// 模拟数据
const mockRecords: ConsumptionRecord[] = [
  {
    id: '1',
    customerId: '1',
    customerName: '张三',
    roomId: 'V01',
    roomType: 'VIP包厢',
    startTime: '2025-01-15 19:00:00',
    endTime: '2025-01-15 22:00:00',
    duration: 3,
    baseAmount: 600,
    discountAmount: 60,
    totalAmount: 540,
    pointsEarned: 54,
    pointsUsed: 0,
    paymentMethod: '微信支付',
    notes: '生日聚会',
    createdAt: '2025-01-15 22:30:00',
    cashierName: '李收银'
  },
  {
    id: '2',
    customerId: '1',
    customerName: '张三',
    roomId: 'M03',
    roomType: '中包厢',
    startTime: '2025-01-10 20:00:00',
    endTime: '2025-01-10 23:00:00',
    duration: 3,
    baseAmount: 450,
    discountAmount: 0,
    totalAmount: 450,
    pointsEarned: 45,
    pointsUsed: 0,
    paymentMethod: '支付宝',
    createdAt: '2025-01-10 23:15:00',
    cashierName: '王收银'
  }
];

// 计算属性
const filteredRecords = computed(() => {
  let filtered = records.value;

  if (dateFrom.value) {
    filtered = filtered.filter(r => r.startTime >= dateFrom.value);
  }

  if (dateTo.value) {
    filtered = filtered.filter(r => r.startTime <= dateTo.value + ' 23:59:59');
  }

  if (roomFilter.value) {
    filtered = filtered.filter(r => r.roomType.includes(roomFilter.value));
  }

  return filtered.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
});

const totalRecords = computed(() => records.value.length);
const totalPages = computed(() => Math.ceil(totalRecords.value / pageSize.value));
const totalAmount = computed(() => records.value.reduce((sum, r) => sum + r.totalAmount, 0));
const averageAmount = computed(() => totalRecords.value > 0 ? Math.round(totalAmount.value / totalRecords.value) : 0);
const totalHours = computed(() => records.value.reduce((sum, r) => sum + r.duration, 0));

// 方法
const formatDateTime = (dateTime: string): string => {
  return new Date(dateTime).toLocaleString('zh-CN');
};

const formatTime = (dateTime: string): string => {
  return new Date(dateTime).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
};

const exportRecords = () => {
  console.log('导出消费记录');
  // TODO: 实现导出功能
};

// 生命周期
onMounted(() => {
  if (props.customer) {
    records.value = mockRecords.filter(r => r.customerId === props.customer!.id);
  }
});
</script>

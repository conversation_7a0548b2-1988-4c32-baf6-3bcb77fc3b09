import { apiMockAdapter } from './mock-adapter';

/**
 * API环境配置
 */
export interface ApiEnvironment {
  useRealBackend: boolean;
  mockApiDelay: number;
  baseUrl: string;
}

/**
 * 默认API环境配置
 */
const defaultApiConfig: ApiEnvironment = {
  useRealBackend: false,  // 默认使用模拟后端
  mockApiDelay: 300,      // 模拟延迟（毫秒）
  baseUrl: '/api'         // API基础路径
};

/**
 * API服务配置
 * 根据环境变量或配置决定是使用真实后端还是模拟数据
 */
export function setupApiServices(config: Partial<ApiEnvironment> = {}): void {
  // 合并配置
  const apiConfig: ApiEnvironment = { ...defaultApiConfig, ...config };

  // 设置API基础路径
  if (apiConfig.baseUrl && apiConfig.baseUrl !== defaultApiConfig.baseUrl) {
    console.info(`API base URL set to: ${apiConfig.baseUrl}`);
    // 这里可以更新axios默认配置或各API服务的baseURL
  }

  // 根据配置决定使用真实后端还是模拟数据
  if (!apiConfig.useRealBackend) {
    console.info('API服务运行在模拟模式');
    // 确保mock adapter已初始化
    if (!apiMockAdapter) {
      console.error('模拟适配器初始化失败');
    } else {
      console.info('模拟适配器已成功初始化，将拦截所有API请求');
      // 确保模拟适配器已启用
      apiMockAdapter.enableMock();
    }
  } else {
    console.info('API服务连接到真实后端');
    // 如果模拟适配器存在，确保关闭它
    if (apiMockAdapter) {
      console.info('关闭模拟适配器');
      apiMockAdapter.disableMock();
    }
    // 在此处可以添加实际后端连接的其他初始化逻辑
  }
}

/**
 * 初始化API服务
 * 应用启动时调用此函数
 */
export function initializeApiServices(): void {
  // 从环境变量或其他配置源读取配置
  const useRealBackend = import.meta.env.VITE_USE_REAL_BACKEND === 'true';
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || defaultApiConfig.baseUrl;
  
  setupApiServices({
    useRealBackend,
    baseUrl: apiBaseUrl
  });

  console.info('API服务初始化完成');
}

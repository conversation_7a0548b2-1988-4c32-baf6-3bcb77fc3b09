// 商品分类
export type ProductCategory = 'beverage' | 'snack' | 'fruit' | 'alcohol' | 'cigarette' | 'other';

// 商品信息
export interface Product {
  id: string;
  name: string;
  price: number;
  category: ProductCategory;
  description?: string;
  image?: string;
  stock: number;
  unit: string; // 单位：瓶、包、份、盒等
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 购物车项目
export interface CartItem {
  productId: string;
  product: Product;
  quantity: number;
  notes?: string;
  subtotal: number;
}

// 订单状态
export type OrderStatus = 'pending' | 'confirmed' | 'preparing' | 'served' | 'cancelled';

// 订单信息
export interface Order {
  id: string;
  roomId: string;
  roomType: string;
  items: CartItem[];
  totalAmount: number;
  totalQuantity: number;
  status: OrderStatus;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string; // 收银员
  servedAt?: string;
}

// 订单表单数据
export interface OrderForm {
  roomId: string;
  items: CartItem[];
  notes?: string;
}

// 商品查询参数
export interface ProductQuery {
  keyword?: string;
  category?: ProductCategory;
  isActive?: boolean;
  page?: number;
  pageSize?: number;
}

// 订单查询参数
export interface OrderQuery {
  roomId?: string;
  status?: OrderStatus;
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  pageSize?: number;
}

// 商品分类配置
export const PRODUCT_CATEGORIES = {
  beverage: { label: '饮料', color: 'blue', icon: 'coffee' },
  snack: { label: '小食', color: 'orange', icon: 'cookie-bite' },
  fruit: { label: '水果', color: 'green', icon: 'apple-alt' },
  alcohol: { label: '酒类', color: 'red', icon: 'wine-bottle' },
  cigarette: { label: '香烟', color: 'gray', icon: 'smoking' },
  other: { label: '其他', color: 'purple', icon: 'box' }
} as const;

// 订单状态配置
export const ORDER_STATUS_CONFIG = {
  pending: { label: '待确认', color: 'yellow', icon: 'clock' },
  confirmed: { label: '已确认', color: 'blue', icon: 'check-circle' },
  preparing: { label: '准备中', color: 'orange', icon: 'spinner' },
  served: { label: '已送达', color: 'green', icon: 'check' },
  cancelled: { label: '已取消', color: 'red', icon: 'times-circle' }
} as const;

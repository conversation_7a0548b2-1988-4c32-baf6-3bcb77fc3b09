/**
 * 收银系统API统一入口
 * 提供所有收银系统API的统一导出
 */

// HTTP客户端

// 类型定义
export type * from "./types";

// 认证工具

// API模块
export * as roomApi from "./room";
export * as orderApi from "./order";
export * as productApi from "./product";
export * as transactionApi from "./transaction";

// 便捷导入
import * as roomApi from "./room";
import * as orderApi from "./order";
import * as productApi from "./product";
import * as transactionApi from "./transaction";

/**
 * 收银系统API集合
 * 提供所有API的统一访问接口
 */
export const cashierApi = {
  room: roomApi,
  order: orderApi,
  product: productApi,
  transaction: transactionApi
};

/**
 * 收银系统API配置
 */
export interface CashierApiConfig {
  baseURL?: string;
  timeout?: number;
  enableLogging?: boolean;
  enableCache?: boolean;
  retryCount?: number;
}

/**
 * 初始化收银系统API配置
 */
export function initCashierApi(config?: CashierApiConfig): void {
  if (config?.baseURL) {
    // 动态设置baseURL
    const { CashierHttp } = require("./http");
    CashierHttp.setBaseURL(config.baseURL);
  }
  
  // 可以在这里添加其他全局配置
  if (config?.enableLogging) {
    console.log("收银系统API日志已启用");
  }
}

// 默认导出
export default cashierApi;

export default {
  path: "/report",
  meta: {
    title: "数据中心",
    rank: 990
  },
  children: [
    {
      path: "/report/index",
      name: "Billing",
      component: () => import("@/views/Report/BillingReport/billingReport.vue"),
      meta: {
        title: "营业报表"
      }
    },
    {
      path: "/report/booking",
      name: "Booking",
      component: () => import("@/views/Report/BookingReport/bookingReport.vue"),
      meta: {
        title: "预定报表"
      }
    },
    {
      path: "/report/old",
      name: "OldNew",
      component: () => import("@/views/Report/OldNewReport/oldNewReport.vue"),
      meta: {
        title: "新旧客对比报表"
        // showLink:false,
      }
    },
    {
      path: "/report/Summary",
      name: "Summary",
      component: () =>
        import("@/views/Report/StudentsReport/SummaryReport.vue"),
      meta: {
        title: "大学生优惠汇总报表"
        // showLink:false,
      }
    },
    {
      path: "/report/Details",
      name: "Details",
      component: () =>
        import("@/views/Report/StudentsReport/DetailsReport.vue"),
      meta: {
        title: "大学生优惠明细报表"
        // showLink:false,
      }
    },{
      path: "/report/bank-coupon",
      name: "BankCouponReport",
      component: () => import("@/views/Report/BankCouponReport/bankCouponReport.vue"),
      meta: {
        title: "银行券管理",
      },
    }
  ]
};

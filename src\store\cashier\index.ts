/**
 * 收银系统 Store 模块入口
 *
 * 这个文件提供了收银系统所有 Store 模块的统一导出
 * 以及一些便捷的组合函数
 */

import { useOrderStore } from "./order";
import { useCashierRoomStore } from "./room";
import { useCashierTransactionStore } from "./transaction";

// 导出所有收银系统 Store
export { useCashierRoomStore, useCashierRoom } from "./room";
export {
  useCashierTransactionStore,
  useCashierTransaction
} from "./transaction";
export { useOrderStore, useCashierOrder } from "./order";

// 导出类型定义
export type { Transaction, RoomOperation } from "./transaction";

/**
 * 收银系统统一状态管理 Hook
 *
 * 这个函数提供了收银系统所有状态的统一访问入口
 * 可以在组件中一次性获取所有需要的状态和方法
 */
export function useCashierStore() {
  const roomStore = useCashierRoomStore();
  const transactionStore = useCashierTransactionStore();
  const orderStore = useOrderStore();

  return {
    // 房间管理
    room: roomStore,

    // 交易管理
    transaction: transactionStore,

    // 订单管理
    order: orderStore,

    // 统一的重置方法
    resetAll: () => {
      roomStore.resetState();
      transactionStore.clearTransactions();
      orderStore.clearAllData();
    }
  };
}

/**
 * 收银系统初始化函数
 *
 * 在收银系统启动时调用，用于初始化必要的状态和数据
 */
export function initializeCashierStore() {
  const { room, order } = useCashierStore();

  // 初始化房间数据
  room.refreshRooms();

  // 初始化订单数据
  order.loadFromLocalStorage();
  order.initializeProducts();

  // 可以在这里添加其他初始化逻辑
  console.log("收银系统 Store 初始化完成");
}

/**
 * 收银系统清理函数
 *
 * 在离开收银系统时调用，用于清理状态和释放资源
 */
export function cleanupCashierStore() {
  const { resetAll } = useCashierStore();

  // 重置所有状态
  resetAll();

  console.log("收银系统 Store 已清理");
}

import { ref } from "vue";
import type { Room } from "../types/room";
import type { OrderForm } from "../types/product";
import { useOrderStore } from "@/store/cashier/order";
import { ElMessage } from "element-plus";
import { useRoomOperations } from "./use-room-operations";

export function useCashierModals() {
  const orderStore = useOrderStore();
  const roomOperations = useRoomOperations();

  // 商品选择弹窗状态
  const showProductModal = ref(false);
  const productModalRoomInfo = ref<{ id: string; type: string }>({
    id: "",
    type: ""
  });

  // 结账确认弹窗状态
  const showCheckoutModal = ref(false);
  const checkoutRoomInfo = ref<Room | null>(null);

  // 开房模态框状态
  const showRoomOpeningModal = ref(false);
  const roomOpeningInfo = ref<Room>({ id: "", type: "", status: "free", area: "V" });

  // 打开商品选择弹窗
  const openProductModal = (room: Room) => {
    productModalRoomInfo.value = { id: room.id, type: room.type };
    showProductModal.value = true;
  };

  // 关闭商品选择弹窗
  const closeProductModal = () => {
    showProductModal.value = false;
  };

  // 确认订单
  const handleOrderConfirm = (orderForm: OrderForm) => {
    orderStore.addOrder(orderForm);
    closeProductModal();
    ElMessage.success("落单成功");
  };

  // 打开结账确认弹窗
  const openCheckoutModal = (room: Room) => {
    checkoutRoomInfo.value = room;
    showCheckoutModal.value = true;
  };

  // 关闭结账确认弹窗
  const closeCheckoutModal = () => {
    showCheckoutModal.value = false;
  };

  // 打开开房模态框
  const openRoomOpeningModal = (room: Room) => {
    roomOpeningInfo.value = room;
    showRoomOpeningModal.value = true;
  };

  // 关闭开房模态框
  const closeRoomOpeningModal = () => {
    showRoomOpeningModal.value = false;
  };

  // 确认开房
  const handleRoomOpeningConfirm = (roomId: string, formData: any) => {
    roomOperations.openRoom(roomId, formData.customerType);
    closeRoomOpeningModal();
    ElMessage.success("开房成功");
  };

  return {
    // state
    showProductModal,
    productModalRoomInfo,
    showCheckoutModal,
    checkoutRoomInfo,
    showRoomOpeningModal,
    roomOpeningInfo,

    // methods
    openProductModal,
    closeProductModal,
    handleOrderConfirm,
    openCheckoutModal,
    closeCheckoutModal,
    openRoomOpeningModal,
    closeRoomOpeningModal,
    handleRoomOpeningConfirm
  };
}

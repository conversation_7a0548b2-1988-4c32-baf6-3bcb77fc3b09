/**
 * 权限管理工具类
 * 用于解析localStorage中的HDJT_PAGEDATA权限数据
 */

// 权限数据类型定义
export interface PermissionData {
  dataItems: Record<string, any>;  // 基础数据字典
  organize: Record<string, any>;   // 组织架构信息
  role: Record<string, any>;       // 角色权限信息
  authorizeMenu?: MenuPermission[]; // 授权菜单列表
}

export interface MenuPermission {
  F_Id: string;                    // 菜单ID
  F_ParentId: string;             // 父菜单ID
  F_EnCode: string;               // 菜单编码
  F_FullName: string;             // 菜单名称
  F_Icon: string;                 // 菜单图标
  F_UrlAddress: string;           // 菜单地址
  F_Target: string;               // 打开方式
  F_IsMenu: number;               // 是否菜单 1-是 0-否
  F_IsExpand: number;             // 是否展开
  F_IsPublic: number;             // 是否公开
  F_AllowEdit: number;            // 允许编辑
  F_AllowDelete: number;          // 允许删除
  F_SortCode: number;             // 排序码
  F_DeleteMark: number;           // 删除标记
  F_EnabledMark: number;          // 启用标记
  F_Description: string;          // 描述
  F_CreatorTime: string;          // 创建时间
  F_CreatorUserId: string;        // 创建用户ID
  F_LastModifyTime: string;       // 最后修改时间
  F_LastModifyUserId: string;     // 最后修改用户ID
  F_DeleteTime: string;           // 删除时间
  F_DeleteUserId: string;         // 删除用户ID
  children?: MenuPermission[];     // 子菜单
}

export interface StandardPermission {
  id: string;
  parentId: string;
  name: string;
  title: string;
  path: string;
  component?: string;
  icon: string;
  rank: number;
  roles: string[];
  permissions: string[];
  isMenu: boolean;
  isPublic: boolean;
  allowEdit: boolean;
  allowDelete: boolean;
  children?: StandardPermission[];
  meta?: {
    title: string;
    icon: string;
    rank: number;
    roles?: string[];
    auths?: string[];
    showLink?: boolean;
    keepAlive?: boolean;
  };
}

/**
 * 权限数据解析器
 */
export class PermissionParser {
  private static readonly STORAGE_KEY = 'HDJT_PAGEDATA';
  private static readonly MODULE_KEY = 'HDJT_CURRENTMODULEID';

  /**
   * 获取原始权限数据
   */
  static getRawPermissionData(): PermissionData | null {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('解析权限数据失败:', error);
      return null;
    }
  }

  /**
   * 获取当前模块ID
   */
  static getCurrentModuleId(): string | null {
    return localStorage.getItem(this.MODULE_KEY);
  }

  /**
   * 获取用户角色列表
   */
  static getUserRoles(): string[] {
    const data = this.getRawPermissionData();
    if (!data?.role) return [];
    
    // 根据实际数据结构调整
    const roles: string[] = [];
    if (data.role.F_EnCode) {
      roles.push(data.role.F_EnCode);
    }
    return roles;
  }

  /**
   * 获取用户权限列表
   */
  static getUserPermissions(): string[] {
    const data = this.getRawPermissionData();
    const currentModuleId = this.getCurrentModuleId();
    
    if (!data?.authorizeMenu || !currentModuleId) return [];

    const permissions: string[] = [];
    
    // 递归收集权限
    const collectPermissions = (menus: MenuPermission[]) => {
      menus.forEach(menu => {
        if (menu.F_EnabledMark === 1 && menu.F_DeleteMark === 0) {
          // 添加基础权限
          permissions.push(menu.F_EnCode);
          
          // 添加操作权限
          if (menu.F_AllowEdit === 1) {
            permissions.push(`${menu.F_EnCode}:edit`);
          }
          if (menu.F_AllowDelete === 1) {
            permissions.push(`${menu.F_EnCode}:delete`);
          }
          
          // 递归处理子菜单
          if (menu.children) {
            collectPermissions(menu.children);
          }
        }
      });
    };

    // 查找当前模块的菜单
    const matchedMenu = data.authorizeMenu.find(
      (item: MenuPermission) => item.F_Id === currentModuleId
    );

    if (matchedMenu?.children) {
      collectPermissions(matchedMenu.children);
    }

    return permissions;
  }

  /**
   * 将原始菜单数据转换为标准权限格式
   */
  static parseMenuToStandardPermission(menu: MenuPermission): StandardPermission {
    return {
      id: menu.F_Id,
      parentId: menu.F_ParentId,
      name: menu.F_EnCode,
      title: menu.F_FullName,
      path: menu.F_UrlAddress || `/${menu.F_EnCode}`,
      icon: menu.F_Icon || 'ep:menu',
      rank: menu.F_SortCode || 999,
      roles: this.getUserRoles(),
      permissions: [menu.F_EnCode],
      isMenu: menu.F_IsMenu === 1,
      isPublic: menu.F_IsPublic === 1,
      allowEdit: menu.F_AllowEdit === 1,
      allowDelete: menu.F_AllowDelete === 1,
      children: menu.children?.map(child => this.parseMenuToStandardPermission(child)),
      meta: {
        title: menu.F_FullName,
        icon: menu.F_Icon || 'ep:menu',
        rank: menu.F_SortCode || 999,
        roles: this.getUserRoles(),
        auths: [
          menu.F_EnCode,
          ...(menu.F_AllowEdit === 1 ? [`${menu.F_EnCode}:edit`] : []),
          ...(menu.F_AllowDelete === 1 ? [`${menu.F_EnCode}:delete`] : [])
        ],
        showLink: menu.F_EnabledMark === 1 && menu.F_DeleteMark === 0,
        keepAlive: false
      }
    };
  }

  /**
   * 获取标准化的权限菜单树
   */
  static getStandardPermissionTree(): StandardPermission[] {
    const data = this.getRawPermissionData();
    const currentModuleId = this.getCurrentModuleId();
    
    if (!data?.authorizeMenu || !currentModuleId) {
      console.warn('未找到权限数据或当前模块ID');
      return [];
    }

    // 查找当前模块的菜单
    const matchedMenu = data.authorizeMenu.find(
      (item: MenuPermission) => item.F_Id === currentModuleId
    );

    if (!matchedMenu?.children) {
      console.warn('未找到当前模块的子菜单');
      return [];
    }

    // 转换为标准格式并过滤有效菜单
    return matchedMenu.children
      .filter(menu => menu.F_EnabledMark === 1 && menu.F_DeleteMark === 0)
      .map(menu => this.parseMenuToStandardPermission(menu))
      .sort((a, b) => a.rank - b.rank);
  }

  /**
   * 检查用户是否有指定权限
   */
  static hasPermission(permission: string): boolean {
    const userPermissions = this.getUserPermissions();
    return userPermissions.includes(permission);
  }

  /**
   * 检查用户是否有指定角色
   */
  static hasRole(role: string): boolean {
    const userRoles = this.getUserRoles();
    return userRoles.includes(role);
  }

  /**
   * 检查用户是否有任一权限
   */
  static hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(permission));
  }

  /**
   * 检查用户是否有任一角色
   */
  static hasAnyRole(roles: string[]): boolean {
    return roles.some(role => this.hasRole(role));
  }
}

// 导出便捷方法
export const {
  getRawPermissionData,
  getCurrentModuleId,
  getUserRoles,
  getUserPermissions,
  getStandardPermissionTree,
  hasPermission,
  hasRole,
  hasAnyPermission,
  hasAnyRole
} = PermissionParser;

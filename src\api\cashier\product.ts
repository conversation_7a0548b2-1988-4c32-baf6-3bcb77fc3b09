import { cashierHttp } from "./http";
import type { CashierApiResponse, CashierPaginatedResponse } from "./types";
import type { Product, ProductCategory } from "@/views/cashier/types/product";

/**
 * 收银系统产品API
 * 处理所有与产品相关的API请求
 */

// 产品查询参数
export interface ProductQueryParams {
  category?: ProductCategory;
  available?: boolean;
  keyword?: string;
  page?: number;
  pageSize?: number;
  priceMin?: number;
  priceMax?: number;
}

// 产品库存信息
export interface ProductStock {
  productId: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  lastUpdated: string;
}

// 产品价格信息
export interface ProductPrice {
  productId: string;
  basePrice: number;
  memberPrice?: number;
  vipPrice?: number;
  promotionPrice?: number;
  effectiveDate?: string;
  expiryDate?: string;
}

/**
 * 获取所有产品
 */
export const getAllProducts = (params?: ProductQueryParams): Promise<CashierApiResponse<Product[]>> => {
  return cashierHttp.get<CashierApiResponse<Product[]>>("/products", { params });
};

/**
 * 分页获取产品列表
 */
export const getProductsPaginated = (params: ProductQueryParams): Promise<CashierPaginatedResponse<Product>> => {
  return cashierHttp.get<CashierPaginatedResponse<Product>>("/products/paginated", { params });
};

/**
 * 获取产品详情
 */
export const getProductById = (productId: string): Promise<CashierApiResponse<Product>> => {
  return cashierHttp.get<CashierApiResponse<Product>>(`/products/${productId}`);
};

/**
 * 按分类获取产品
 */
export const getProductsByCategory = (category: ProductCategory): Promise<CashierApiResponse<Product[]>> => {
  return cashierHttp.get<CashierApiResponse<Product[]>>(`/products/category/${category}`);
};

/**
 * 获取可用产品（库存充足）
 */
export const getAvailableProducts = (): Promise<CashierApiResponse<Product[]>> => {
  return cashierHttp.get<CashierApiResponse<Product[]>>("/products/available");
};

/**
 * 搜索产品
 */
export const searchProducts = (params: {
  keyword: string;
  category?: ProductCategory;
  page?: number;
  pageSize?: number;
}): Promise<CashierPaginatedResponse<Product>> => {
  return cashierHttp.get<CashierPaginatedResponse<Product>>("/products/search", { params });
};

/**
 * 获取热门产品
 */
export const getPopularProducts = (params?: {
  limit?: number;
  category?: ProductCategory;
  period?: "day" | "week" | "month";
}): Promise<CashierApiResponse<Product[]>> => {
  return cashierHttp.get<CashierApiResponse<Product[]>>("/products/popular", { params });
};

/**
 * 获取推荐产品
 */
export const getRecommendedProducts = (params?: {
  roomType?: string;
  customerType?: string;
  limit?: number;
}): Promise<CashierApiResponse<Product[]>> => {
  return cashierHttp.get<CashierApiResponse<Product[]>>("/products/recommended", { params });
};

/**
 * 获取产品库存信息
 */
export const getProductStock = (productId: string): Promise<CashierApiResponse<ProductStock>> => {
  return cashierHttp.get<CashierApiResponse<ProductStock>>(`/products/${productId}/stock`);
};

/**
 * 批量获取产品库存信息
 */
export const getBatchProductStock = (productIds: string[]): Promise<CashierApiResponse<ProductStock[]>> => {
  return cashierHttp.post<CashierApiResponse<ProductStock[]>>("/products/stock/batch", {
    data: { productIds }
  });
};

/**
 * 获取产品价格信息
 */
export const getProductPrice = (productId: string): Promise<CashierApiResponse<ProductPrice>> => {
  return cashierHttp.get<CashierApiResponse<ProductPrice>>(`/products/${productId}/price`);
};

/**
 * 批量获取产品价格信息
 */
export const getBatchProductPrice = (productIds: string[]): Promise<CashierApiResponse<ProductPrice[]>> => {
  return cashierHttp.post<CashierApiResponse<ProductPrice[]>>("/products/price/batch", {
    data: { productIds }
  });
};

/**
 * 检查产品可用性
 */
export const checkProductAvailability = (
  productId: string,
  quantity: number
): Promise<CashierApiResponse<{
  available: boolean;
  currentStock: number;
  requestedQuantity: number;
  message: string;
}>> => {
  return cashierHttp.post<CashierApiResponse<any>>(`/products/${productId}/check-availability`, {
    data: { quantity }
  });
};

/**
 * 批量检查产品可用性
 */
export const batchCheckProductAvailability = (items: Array<{
  productId: string;
  quantity: number;
}>): Promise<CashierApiResponse<Array<{
  productId: string;
  available: boolean;
  currentStock: number;
  requestedQuantity: number;
  message: string;
}>>> => {
  return cashierHttp.post<CashierApiResponse<any>>("/products/check-availability/batch", {
    data: { items }
  });
};

/**
 * 获取产品分类列表
 */
export const getProductCategories = (): Promise<CashierApiResponse<Array<{
  category: ProductCategory;
  name: string;
  description: string;
  productCount: number;
}>>> => {
  return cashierHttp.get<CashierApiResponse<any>>("/products/categories");
};

/**
 * 获取产品统计信息
 */
export const getProductStatistics = (): Promise<CashierApiResponse<{
  totalProducts: number;
  availableProducts: number;
  outOfStockProducts: number;
  lowStockProducts: number;
  categoriesCount: number;
  totalValue: number;
}>> => {
  return cashierHttp.get<CashierApiResponse<any>>("/products/statistics");
};

/**
 * 预留产品库存（下单时临时锁定库存）
 */
export const reserveProductStock = (items: Array<{
  productId: string;
  quantity: number;
}>): Promise<CashierApiResponse<{
  reservationId: string;
  expiresAt: string;
  items: Array<{
    productId: string;
    reservedQuantity: number;
    success: boolean;
    message?: string;
  }>;
}>> => {
  return cashierHttp.post<CashierApiResponse<any>>("/products/reserve-stock", {
    data: { items }
  });
};

/**
 * 释放预留的产品库存
 */
export const releaseProductStock = (reservationId: string): Promise<CashierApiResponse<{
  success: boolean;
  message: string;
}>> => {
  return cashierHttp.post<CashierApiResponse<any>>(`/products/release-stock/${reservationId}`);
};

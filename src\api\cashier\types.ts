import type {
  Method,
  AxiosError,
  AxiosResponse,
  AxiosRequestConfig
} from "axios";

// 收银系统认证结果类型
export type CashierAuthResult = {
  accessToken?: string;
  refreshToken?: string;
  expires?: string;
  cashierInfo?: {
    id: string;
    name: string;
    role: string;
    permissions: string[];
  };
};

// 收银系统请求方法类型
export type CashierRequestMethods = Extract<
  Method,
  "get" | "post" | "put" | "delete" | "patch" | "option" | "head"
>;

// 收银系统HTTP错误类型
export interface CashierHttpError extends AxiosError {
  isCancelRequest?: boolean;
}

// 收银系统HTTP响应类型
export interface CashierHttpResponse extends AxiosResponse {
  config: CashierHttpRequestConfig;
}

// 收银系统HTTP请求配置类型
export interface CashierHttpRequestConfig extends AxiosRequestConfig {
  beforeRequestCallback?: (request: CashierHttpRequestConfig) => void;
  beforeResponseCallback?: (response: CashierHttpResponse) => void;
}

// 收银系统API响应基础结构
export interface CashierApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  code: number;
  timestamp: string;
  traceId?: string;
}

// 收银系统分页响应结构
export interface CashierPaginatedResponse<T = any> {
  success: boolean;
  data: {
    items: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  message: string;
  code: number;
  timestamp: string;
}

// 收银系统错误响应结构
export interface CashierErrorResponse {
  success: false;
  message: string;
  code: number;
  errors?: {
    field: string;
    message: string;
  }[];
  timestamp: string;
  traceId?: string;
}

// 收银系统认证token结构
export interface CashierTokenData {
  accessToken: string;
  refreshToken: string;
  expires: string;
  tokenType: string;
}

// 收银系统用户信息
export interface CashierUserInfo {
  id: string;
  username: string;
  name: string;
  role: string;
  permissions: string[];
  storeId: string;
  storeName: string;
  avatar?: string;
  lastLoginTime?: string;
}

// 收银系统登录请求
export interface CashierLoginRequest {
  username: string;
  password: string;
  storeId?: string;
  deviceId?: string;
}

// 收银系统登录响应
export interface CashierLoginResponse {
  token: CashierTokenData;
  user: CashierUserInfo;
  permissions: string[];
  storeInfo: {
    id: string;
    name: string;
    address: string;
    phone: string;
  };
}

// 收银系统刷新token请求
export interface CashierRefreshTokenRequest {
  refreshToken: string;
}

// 收银系统API请求配置选项
export interface CashierApiOptions {
  showLoading?: boolean;
  showError?: boolean;
  timeout?: number;
  retryCount?: number;
  cache?: boolean;
  cacheTime?: number;
}

// 收银系统HTTP类默认导出
export default class CashierHttp {
  request<T>(
    method: CashierRequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: CashierHttpRequestConfig
  ): Promise<T>;
  post<T, P>(
    url: string,
    params?: P,
    config?: CashierHttpRequestConfig
  ): Promise<T>;
  get<T, P>(
    url: string,
    params?: P,
    config?: CashierHttpRequestConfig
  ): Promise<T>;
  put<T, P>(
    url: string,
    params?: P,
    config?: CashierHttpRequestConfig
  ): Promise<T>;
  delete<T, P>(
    url: string,
    params?: P,
    config?: CashierHttpRequestConfig
  ): Promise<T>;
  patch<T, P>(
    url: string,
    params?: P,
    config?: CashierHttpRequestConfig
  ): Promise<T>;
}

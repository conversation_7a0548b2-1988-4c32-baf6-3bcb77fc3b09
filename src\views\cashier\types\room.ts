// 房间相关类型定义

export type RoomStatus =
  | "occupied"    // 占用中
  | "free"        // 可用
  | "cleaning"    // 待清洁
  | "booked"      // 已预订
  | "maintenance" // 维修中
  | "disabled"    // 已停用
  | "checkout"    // 结账中
  | "transfer"    // 换房中
  | "overtime"    // 超时使用
  | "vip-service" // VIP服务中
  | "event"       // 活动进行中
  | "reserved";   // 内部保留

export type RoomArea = "V" | "M" | "S" | "X" | "P" | "G"; // VIP区、中包区、小包区、大包区、Party厅、花园VIP

export interface Room {
  id: string;
  type: string;
  status: RoomStatus;
  area: RoomArea;
  startTime?: string;
  customer?: string;
  duration?: string;
  total?: number;
  items?: ConsumptionItem[];
}

export interface ConsumptionItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  total: number;
}

export interface RoomStatusConfig {
  key: RoomStatus;
  label: string;
  color: string;
  count: number;
}

export interface AreaConfig {
  key: RoomArea | "all";
  label: string;
  icon?: string;
  headerClass?: string;
  textColor?: string;
  dotColor?: string;
  tagType?: string;
}

export interface QuickAction {
  key: string;
  label: string;
  icon: string;
}

export interface RecentOpening {
  roomId: string;
  roomType: string;
  time: string;
}

export interface RoomAction {
  key: string;
  label: string;
  type: string;
  icon: string;
}

import MockAdapter from 'axios-mock-adapter';
import { sharedAxiosInstance } from './base-api'; 
import { MOCK_ROOMS } from '@/views/cashier/constants/room-config';
import type { Room, RoomStatus } from '@/views/cashier/types/room';
import type { Order, Product, OrderForm } from '@/views/cashier/types/product';

/**
 * API模拟适配器
 * 提供模拟数据响应，用于前后端分离开发或演示
 */
export class ApiMockAdapter {
  // 将mock改为protected以便setup.ts可以访问
  protected mock: MockAdapter;
  private rooms: Room[] = [...MOCK_ROOMS];
  private orders: Order[] = [];
  private products: Product[] = [];

  constructor() {
    // 创建一个新的模拟适配器实例，使用共享的axios实例，设置延迟以模拟真实网络请求
    this.mock = new MockAdapter(sharedAxiosInstance, { delayResponse: 300 });
    this.initializeMockData();
    this.setupMockEndpoints();
  }
  
  /**
   * 启用模拟适配器
   */
  public enableMock(): void {
    if (this.mock) {
      this.mock.reset();
      this.setupMockEndpoints();
    }
  }
  
  /**
   * 禁用模拟适配器
   */
  public disableMock(): void {
    if (this.mock) {
      this.mock.reset();
      this.mock.restore();
    }
  }
  
  /**
   * 获取模拟适配器实例
   */
  public getMockAdapter(): MockAdapter {
    return this.mock;
  }

  /**
   * 初始化模拟数据
   */
  private initializeMockData(): void {
    // 从localStorage恢复订单数据
    try {
      const storedOrders = localStorage.getItem('cashier_orders');
      if (storedOrders) {
        this.orders = JSON.parse(storedOrders);
      }
    } catch (error) {
      console.error('从本地存储加载订单失败:', error);
    }

    // 初始化产品数据
    this.products = [
      {
        id: "1",
        name: "可口可乐",
        price: 8,
        category: "beverage",
        description: "经典可乐",
        stock: 50,
        unit: "瓶",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      {
        id: "2",
        name: "薯片",
        price: 15,
        category: "snack",
        description: "香脆薯片",
        stock: 30,
        unit: "包",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      // ... 其他产品 (省略，与现有系统保持一致)
    ];
  }

  /**
   * 设置模拟API端点
   */
  private setupMockEndpoints(): void {
    this.setupRoomEndpoints();
    this.setupOrderEndpoints();
    this.setupProductEndpoints();
    // 客户和预订API的模拟端点可以按需添加
  }

  /**
   * 创建通用的API响应
   */
  private createResponse<T>(data: T, message: string = 'success', code: number = 200) {
    return {
      code,
      message,
      data
    };
  }

  /**
   * 设置房间相关的模拟端点
   */
  private setupRoomEndpoints(): void {
    // 获取所有房间
    this.mock.onGet('/api/rooms').reply(() => {
      return [200, this.createResponse(this.rooms)];
    });

    // 获取单个房间
    this.mock.onGet(new RegExp('/api/rooms/[^/]+')).reply((config) => {
      const roomId = config.url?.split('/').pop();
      const room = this.rooms.find(r => r.id === roomId);
      
      if (room) {
        return [200, this.createResponse(room)];
      } else {
        return [404, this.createResponse(null, '房间未找到', 404)];
      }
    });

    // 更新房间状态
    this.mock.onPatch(new RegExp('/api/rooms/[^/]+/status')).reply((config) => {
      const roomId = config.url?.split('/')[3];
      const data = JSON.parse(config.data);
      const room = this.rooms.find(r => r.id === roomId);
      
      if (room) {
        room.status = data.status as RoomStatus;
        return [200, this.createResponse(room)];
      } else {
        return [404, this.createResponse(null, '房间未找到', 404)];
      }
    });

    // 开房操作
    this.mock.onPost(new RegExp('/api/rooms/[^/]+/open')).reply((config) => {
      const roomId = config.url?.split('/')[3];
      const data = JSON.parse(config.data);
      const room = this.rooms.find(r => r.id === roomId);
      
      if (room) {
        if (room.status !== 'free') {
          return [400, this.createResponse(null, '房间不可用', 400)];
        }
        
        room.status = 'occupied';
        room.customer = data.customer;
        room.startTime = new Date().toISOString();
        
        return [200, this.createResponse(room)];
      } else {
        return [404, this.createResponse(null, '房间未找到', 404)];
      }
    });

    // 结账操作
    this.mock.onPost(new RegExp('/api/rooms/[^/]+/checkout')).reply((config) => {
      const roomId = config.url?.split('/')[3];
      const room = this.rooms.find(r => r.id === roomId);
      
      if (room) {
        if (room.status !== 'occupied') {
          return [400, this.createResponse(null, '房间不是占用状态', 400)];
        }
        
        room.status = 'cleaning';
        room.customer = undefined;
        room.startTime = undefined;
        
        return [200, this.createResponse(room)];
      } else {
        return [404, this.createResponse(null, '房间未找到', 404)];
      }
    });

    // 按区域获取房间
    this.mock.onGet(new RegExp('/api/rooms/area/[^/]+')).reply((config) => {
      const area = config.url?.split('/').pop();
      const filteredRooms = this.rooms.filter(r => r.area === area);
      return [200, this.createResponse(filteredRooms)];
    });

    // 按状态获取房间
    this.mock.onGet(new RegExp('/api/rooms/status/[^/]+')).reply((config) => {
      const status = config.url?.split('/').pop() as RoomStatus;
      const filteredRooms = this.rooms.filter(r => r.status === status);
      return [200, this.createResponse(filteredRooms)];
    });

    // 搜索房间
    this.mock.onGet('/api/rooms/search').reply((config) => {
      const keyword = config.params?.keyword?.toLowerCase();
      
      if (!keyword) {
        return [200, this.createResponse(this.rooms)];
      }
      
      const filteredRooms = this.rooms.filter(
        room => room.id.toLowerCase().includes(keyword) || 
               (room.customer && room.customer.toLowerCase().includes(keyword))
      );
      
      return [200, this.createResponse(filteredRooms)];
    });
  }

  /**
   * 设置订单相关的模拟端点
   */
  private setupOrderEndpoints(): void {
    // 创建订单
    this.mock.onPost('/api/orders').reply((config) => {
      const orderForm = JSON.parse(config.data) as OrderForm;
      const orderId = `ORDER_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      
      const order: Order = {
        id: orderId,
        roomId: orderForm.roomId,
        roomType: this.getRoomTypeById(orderForm.roomId),
        items: orderForm.items,
        totalAmount: orderForm.items.reduce((sum, item) => sum + item.subtotal, 0),
        totalQuantity: orderForm.items.reduce((sum, item) => sum + item.quantity, 0),
        status: 'pending',
        notes: orderForm.notes,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'current_cashier' // 模拟当前收银员
      };
      
      this.orders.push(order);
      this.saveOrdersToLocalStorage();
      
      return [201, this.createResponse(order)];
    });

    // 获取订单详情
    this.mock.onGet(new RegExp('/api/orders/[^/]+')).reply((config) => {
      const orderId = config.url?.split('/').pop();
      const order = this.orders.find(o => o.id === orderId);
      
      if (order) {
        return [200, this.createResponse(order)];
      } else {
        return [404, this.createResponse(null, '订单未找到', 404)];
      }
    });

    // 获取房间订单
    this.mock.onGet('/api/orders/room').reply((config) => {
      const roomId = config.params?.roomId;
      const roomOrders = this.orders.filter(order => order.roomId === roomId);
      return [200, this.createResponse(roomOrders)];
    });

    // 更新订单状态
    this.mock.onPatch(new RegExp('/api/orders/[^/]+/status')).reply((config) => {
      const orderId = config.url?.split('/')[3];
      const data = JSON.parse(config.data);
      const order = this.orders.find(o => o.id === orderId);
      
      if (order) {
        order.status = data.status;
        order.updatedAt = new Date().toISOString();
        
        if (data.status === 'served') {
          order.servedAt = new Date().toISOString();
        }
        
        this.saveOrdersToLocalStorage();
        return [200, this.createResponse(order)];
      } else {
        return [404, this.createResponse(null, '订单未找到', 404)];
      }
    });

    // 取消订单
    this.mock.onPost(new RegExp('/api/orders/[^/]+/cancel')).reply((config) => {
      const orderId = config.url?.split('/')[3];
      const order = this.orders.find(o => o.id === orderId);
      
      if (order) {
        order.status = 'cancelled';
        order.updatedAt = new Date().toISOString();
        this.saveOrdersToLocalStorage();
        return [200, this.createResponse(order)];
      } else {
        return [404, this.createResponse(null, '订单未找到', 404)];
      }
    });

    // 获取待处理订单
    this.mock.onGet('/api/orders/status/pending').reply(() => {
      const pendingOrders = this.orders.filter(o => o.status === 'pending');
      return [200, this.createResponse(pendingOrders)];
    });

    // 按状态获取订单
    this.mock.onGet(new RegExp('/api/orders/status/[^/]+')).reply((config) => {
      const status = config.url?.split('/').pop();
      const filteredOrders = this.orders.filter(o => o.status === status);
      return [200, this.createResponse(filteredOrders)];
    });

    // 结账房间所有订单
    this.mock.onPost(new RegExp('/api/orders/room/[^/]+/checkout')).reply((config) => {
      const roomId = config.url?.split('/')[4];
      
      // 标记所有该房间订单为已完成
      const roomOrders = this.orders.filter(o => o.roomId === roomId);
      if (roomOrders.length > 0) {
        roomOrders.forEach(order => {
          order.status = 'served';
          order.updatedAt = new Date().toISOString();
          order.servedAt = new Date().toISOString();
        });
        
        // 从订单列表中移除
        this.orders = this.orders.filter(o => o.roomId !== roomId);
        this.saveOrdersToLocalStorage();
        
        return [200, this.createResponse({ success: true, message: '结账成功' })];
      } else {
        return [200, this.createResponse({ success: true, message: '没有需要结账的订单' })];
      }
    });
  }

  /**
   * 设置产品相关的模拟端点
   */
  private setupProductEndpoints(): void {
    // 获取所有产品
    this.mock.onGet('/api/products').reply(() => {
      return [200, this.createResponse(this.products)];
    });

    // 获取产品详情
    this.mock.onGet(new RegExp('/api/products/[^/]+')).reply((config) => {
      const productId = config.url?.split('/').pop();
      const product = this.products.find(p => p.id === productId);
      
      if (product) {
        return [200, this.createResponse(product)];
      } else {
        return [404, this.createResponse(null, '商品未找到', 404)];
      }
    });

    // 按分类获取产品
    this.mock.onGet(new RegExp('/api/products/category/[^/]+')).reply((config) => {
      const category = config.url?.split('/').pop();
      const filteredProducts = this.products.filter(p => p.category === category);
      return [200, this.createResponse(filteredProducts)];
    });

    // 搜索产品
    this.mock.onGet('/api/products/search').reply((config) => {
      const keyword = config.params?.keyword?.toLowerCase();
      const category = config.params?.category;
      let filteredProducts = [...this.products];
      
      if (category) {
        filteredProducts = filteredProducts.filter(p => p.category === category);
      }
      
      if (keyword) {
        filteredProducts = filteredProducts.filter(
          p => p.name.toLowerCase().includes(keyword) || 
               p.description?.toLowerCase().includes(keyword)
        );
      }
      
      // 处理分页
      const page = config.params?.page || 1;
      const pageSize = config.params?.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const pagedProducts = filteredProducts.slice(start, end);
      
      return [200, this.createResponse(pagedProducts)];
    });

    // 获取热门商品
    this.mock.onGet('/api/products/popular').reply((config) => {
      const limit = config.params?.limit || 10;
      // 简单模拟热门商品，实际应基于订单量计算
      const popularProducts = [...this.products].sort(() => 0.5 - Math.random()).slice(0, limit);
      return [200, this.createResponse(popularProducts)];
    });
  }

  /**
   * 获取房间类型
   */
  private getRoomTypeById(roomId: string): string {
    if (roomId.startsWith('V')) return 'VIP包厢';
    if (roomId.startsWith('M')) return '中包厢';
    if (roomId.startsWith('S')) return '小包厢';
    return '普通包厢';
  }

  /**
   * 将订单保存到本地存储
   */
  private saveOrdersToLocalStorage(): void {
    try {
      localStorage.setItem('cashier_orders', JSON.stringify(this.orders));
    } catch (error) {
      console.error('保存订单到本地存储失败:', error);
    }
  }
}

// 创建一个全局单例实例
export const apiMockAdapter = new ApiMockAdapter();

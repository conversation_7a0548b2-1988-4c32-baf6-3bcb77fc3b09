# 平台本地运行端口号
VITE_PORT = 8848

# 开发环境读取配置文件路径
VITE_PUBLIC_PATH = /

# 开发环境路由历史模式（Hash模式传"hash"、HTML5模式传"h5"、Hash模式带base参数传"hash,base参数"、HTML5模式带base参数传"h5,base参数"）
VITE_ROUTER_HISTORY = "hash"

# 管理系统API配置
VITE_MANAGEMENT_API_BASE_URL = "http://localhost:5010/"

# 开发模式API请求方式配置
# true: 使用Vite代理（推荐，避免跨域问题）
# false: 直连后端（需要后端支持CORS）
VITE_USE_PROXY = true

# Vite代理目标配置（当VITE_USE_PROXY=true时生效）
# 可选值：
# - http://localhost:5010 (本地后端)
# - http://*************:5010 (局域网后端)
# - lan (快捷方式，等同于 http://*************:5010)
VITE_PROXY_TARGET = "http://localhost:5010"

# 收银系统API配置
VITE_CASHIER_API_BASE_URL = "https://cashier-api.tang-hui.com.cn/"
VITE_CASHIER_API_TIMEOUT = 15000
